/**
 * Administration System Tests
 * Tests for user management, role management, permissions, and audit system
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import mongoose from 'mongoose';
import app from '../server/index.js';
import User from '../server/models/User.js';
import Role from '../server/models/Role.js';
import Permission from '../server/models/Permission.js';
import AuditLog from '../server/models/AuditLog.js';

// Test database connection
const MONGODB_URI = process.env.MONGODB_TEST_URI;// || 'mongodb://localhost:27017/inception_hms_test';

describe('Administration System', () => {
  let adminToken;
  let testUser;
  let testRole;
  let testPermission;

  beforeEach(async () => {
    // Connect to test database
    await mongoose.connect(MONGODB_URI);
    
    // Clear test data
    await User.deleteMany({});
    await Role.deleteMany({});
    await Permission.deleteMany({});
    await AuditLog.deleteMany({});

    // Create test permission
    testPermission = await Permission.create({
      module: 'admin',
      action: 'view',
      resource: 'users',
      description: 'View user accounts',
      category: 'User Management'
    });

    // Create test role
    testRole = await Role.create({
      name: 'Test Admin',
      description: 'Test administrator role',
      level: 10,
      defaultPermissions: [testPermission._id],
      isSystemRole: false
    });

    // Create admin user
    const adminUser = await User.create({
      username: 'testadmin',
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Test',
      lastName: 'Admin',
      role: testRole._id,
      permissions: [testPermission._id],
      isActive: true
    });

    // Login to get token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });

    adminToken = loginResponse.body.token;
  });

  afterEach(async () => {
    // Clean up test data
    await User.deleteMany({});
    await Role.deleteMany({});
    await Permission.deleteMany({});
    await AuditLog.deleteMany({});
    await mongoose.connection.close();
  });

  describe('User Management', () => {
    it('should get all users', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should create a new user', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: testRole._id,
        department: 'IT'
      };

      const response = await request(app)
        .post('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(userData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.email).toBe(userData.email);
    });

    it('should update user information', async () => {
      // Create test user first
      testUser = await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        role: testRole._id,
        isActive: true
      });

      const updateData = {
        firstName: 'Updated',
        lastName: 'User',
        department: 'HR'
      };

      const response = await request(app)
        .put(`/api/admin/users/${testUser._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.firstName).toBe('Updated');
    });

    it('should assign role to user', async () => {
      // Create test user
      testUser = await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        role: testRole._id,
        isActive: true
      });

      const response = await request(app)
        .put(`/api/admin/users/${testUser._id}/role`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ roleId: testRole._id });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Role assigned');
    });
  });

  describe('Role Management', () => {
    it('should get all roles', async () => {
      const response = await request(app)
        .get('/api/admin/roles')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should create a new role', async () => {
      const roleData = {
        name: 'Test Role',
        description: 'A test role',
        level: 5,
        defaultPermissions: [testPermission._id]
      };

      const response = await request(app)
        .post('/api/admin/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(roleData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(roleData.name);
    });

    it('should assign permissions to role', async () => {
      const response = await request(app)
        .put(`/api/admin/roles/${testRole._id}/permissions`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ permissionIds: [testPermission._id] });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Permissions assigned');
    });
  });

  describe('Permission Management', () => {
    it('should get all permissions', async () => {
      const response = await request(app)
        .get('/api/admin/permissions')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should create a new permission', async () => {
      const permissionData = {
        module: 'test',
        action: 'create',
        resource: 'test-resource',
        description: 'Test permission',
        category: 'Test Category'
      };

      const response = await request(app)
        .post('/api/admin/permissions')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(permissionData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.module).toBe(permissionData.module);
    });
  });

  describe('Audit System', () => {
    it('should get audit logs', async () => {
      // Create a test audit log
      await AuditLog.create({
        user: testUser?._id,
        action: 'Test Action',
        resource: 'test',
        resourceId: 'test-id',
        details: 'Test audit log entry',
        ipAddress: '127.0.0.1',
        userAgent: 'Test Agent'
      });

      const response = await request(app)
        .get('/api/admin/audit-logs')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should filter audit logs by action', async () => {
      // Create test audit logs
      await AuditLog.create({
        user: testUser?._id,
        action: 'User Created',
        resource: 'users',
        details: 'Test user creation',
        ipAddress: '127.0.0.1'
      });

      await AuditLog.create({
        user: testUser?._id,
        action: 'User Updated',
        resource: 'users',
        details: 'Test user update',
        ipAddress: '127.0.0.1'
      });

      const response = await request(app)
        .get('/api/admin/audit-logs?action=User Created')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBe(1);
      expect(response.body.data[0].action).toBe('User Created');
    });
  });

  describe('System Statistics', () => {
    it('should get system statistics', async () => {
      const response = await request(app)
        .get('/api/admin/system/stats')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('totalUsers');
      expect(response.body.data).toHaveProperty('totalRoles');
      expect(response.body.data).toHaveProperty('totalPermissions');
    });
  });

  describe('Authentication & Authorization', () => {
    it('should deny access without token', async () => {
      const response = await request(app)
        .get('/api/admin/users');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should deny access with invalid token', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid user ID', async () => {
      const response = await request(app)
        .get('/api/admin/users/invalid-id')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should handle duplicate email creation', async () => {
      const userData = {
        username: 'duplicate',
        email: '<EMAIL>', // Same as admin user
        password: 'password123',
        firstName: 'Duplicate',
        lastName: 'User',
        role: testRole._id
      };

      const response = await request(app)
        .post('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(userData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });
});

// Helper function to run tests
export const runAdministrationTests = () => {
  console.log('Running Administration System Tests...');
  // Tests will be executed by Jest
};
