/**
 * Jest Global Setup
 * Runs once before all tests
 */

import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';

let mongoServer;

export default async function globalSetup() {
  console.log('🚀 Starting global test setup...');
  
  try {
    // Start in-memory MongoDB server for testing
    mongoServer = await MongoMemoryServer.create({
      instance: {
        dbName: 'vaidya_hms_test'
      }
    });
    
    const mongoUri = mongoServer.getUri();
    
    // Set the MongoDB URI for tests
    process.env.MONGODB_TEST_URI = mongoUri;
    
    console.log(`📦 MongoDB Memory Server started at: ${mongoUri}`);
    
    // Store the server instance globally so it can be stopped in teardown
    global.__MONGOSERVER__ = mongoServer;
    
    console.log('✅ Global test setup completed');
  } catch (error) {
    console.error('❌ Global test setup failed:', error);
    throw error;
  }
}
