/**
 * Jest Global Teardown
 * Runs once after all tests
 */

import mongoose from 'mongoose';

export default async function globalTeardown() {
  console.log('🧹 Starting global test teardown...');
  
  try {
    // Close mongoose connections
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
      console.log('📦 Mongoose connection closed');
    }
    
    // Stop MongoDB Memory Server
    if (global.__MONGOSERVER__) {
      await global.__MONGOSERVER__.stop();
      console.log('🛑 MongoDB Memory Server stopped');
    }
    
    console.log('✅ Global test teardown completed');
  } catch (error) {
    console.error('❌ Global test teardown failed:', error);
    throw error;
  }
}
