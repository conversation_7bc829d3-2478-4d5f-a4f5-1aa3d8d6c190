/**
 * Jest Test Setup
 * Global test configuration and utilities
 */

import { jest } from '@jest/globals';
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.MONGODB_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/vaidya_hms_test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_EXPIRE = '1h';

// Global test timeout
jest.setTimeout(30000);

// Mock console methods in test environment
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test utilities
global.testUtils = {
  // Generate random test data
  generateRandomString: (length = 10) => {
    return Math.random().toString(36).substring(2, length + 2);
  },
  
  // Generate test user data
  generateTestUser: (overrides = {}) => ({
    username: `testuser_${Date.now()}`,
    email: `test_${Date.now()}@example.com`,
    password: 'TestPassword123!',
    firstName: 'Test',
    lastName: 'User',
    department: 'IT',
    isActive: true,
    ...overrides
  }),
  
  // Generate test role data
  generateTestRole: (overrides = {}) => ({
    name: `TestRole_${Date.now()}`,
    description: 'Test role description',
    level: 5,
    isSystemRole: false,
    ...overrides
  }),
  
  // Generate test permission data
  generateTestPermission: (overrides = {}) => ({
    module: 'test',
    action: 'view',
    resource: 'test-resource',
    description: 'Test permission',
    category: 'Test Category',
    ...overrides
  }),
  
  // Wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Clean up test data
  cleanupTestData: async (models) => {
    const cleanupPromises = Object.values(models).map(model => 
      model.deleteMany({})
    );
    await Promise.all(cleanupPromises);
  }
};

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Mock external services
jest.mock('../server/services/emailService', () => ({
  sendEmail: jest.fn().mockResolvedValue(true),
  sendPasswordResetEmail: jest.fn().mockResolvedValue(true),
  sendWelcomeEmail: jest.fn().mockResolvedValue(true)
}));

jest.mock('../server/services/notificationService', () => ({
  createNotification: jest.fn().mockResolvedValue(true),
  sendNotification: jest.fn().mockResolvedValue(true)
}));

// Mock PDF generation
jest.mock('../server/utils/pdfGenerator', () => ({
  generatePDF: jest.fn().mockResolvedValue(Buffer.from('mock-pdf-content')),
  generateReport: jest.fn().mockResolvedValue(Buffer.from('mock-report-content'))
}));

// Custom matchers
expect.extend({
  toBeValidObjectId(received) {
    const pass = /^[0-9a-fA-F]{24}$/.test(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid ObjectId`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid ObjectId`,
        pass: false,
      };
    }
  },
  
  toHaveValidationError(received, field) {
    const pass = received.errors && received.errors[field];
    if (pass) {
      return {
        message: () => `expected validation error for field ${field} not to exist`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected validation error for field ${field} to exist`,
        pass: false,
      };
    }
  }
});

// Test database connection helper
global.connectTestDB = async () => {
  const mongoose = await import('mongoose');
  if (mongoose.connection.readyState === 0) {
    await mongoose.connect(process.env.MONGODB_URI);
  }
};

// Test database cleanup helper
global.disconnectTestDB = async () => {
  const mongoose = await import('mongoose');
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
  }
};

console.log('🧪 Test setup completed');
