import React, { useState, useEffect } from 'react';
import {
  Database,
  Download,
  Upload,
  Refresh<PERSON><PERSON>,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Play,
  Pause,
  Settings,
  Calendar,
  HardDrive,
  Shield,
  Trash2,
  Eye
} from 'lucide-react';

interface BackupRecord {
  id: string;
  name: string;
  type: 'automatic' | 'manual';
  size: string;
  status: 'completed' | 'failed' | 'in_progress';
  createdAt: string;
  duration: string;
  description?: string;
}

interface MaintenanceTask {
  id: string;
  name: string;
  description: string;
  type: 'database' | 'system' | 'security';
  status: 'pending' | 'running' | 'completed' | 'failed';
  lastRun?: string;
  nextRun?: string;
  frequency: string;
}

export function BackupMaintenance() {
  const [activeTab, setActiveTab] = useState('backups');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Backup states
  const [backups, setBackups] = useState<BackupRecord[]>([]);
  const [backupInProgress, setBackupInProgress] = useState(false);
  const [backupProgress, setBackupProgress] = useState(0);

  // Maintenance states
  const [maintenanceTasks, setMaintenanceTasks] = useState<MaintenanceTask[]>([]);
  const [systemHealth, setSystemHealth] = useState({
    database: { status: 'healthy', lastCheck: '2024-01-15 10:30:00' },
    storage: { status: 'healthy', usage: '45%', available: '2.1 TB' },
    memory: { status: 'healthy', usage: '68%', available: '12.8 GB' },
    cpu: { status: 'healthy', usage: '23%', load: '0.45' }
  });

  // Mock data
  const mockBackups: BackupRecord[] = [
    {
      id: '1',
      name: 'Daily Backup - 2024-01-15',
      type: 'automatic',
      size: '2.4 GB',
      status: 'completed',
      createdAt: '2024-01-15 02:00:00',
      duration: '12 minutes',
      description: 'Scheduled daily backup including all patient data and system configurations'
    },
    {
      id: '2',
      name: 'Manual Backup - Pre-Update',
      type: 'manual',
      size: '2.3 GB',
      status: 'completed',
      createdAt: '2024-01-14 16:45:00',
      duration: '8 minutes',
      description: 'Manual backup before system update'
    },
    {
      id: '3',
      name: 'Daily Backup - 2024-01-14',
      type: 'automatic',
      size: '2.2 GB',
      status: 'completed',
      createdAt: '2024-01-14 02:00:00',
      duration: '11 minutes'
    },
    {
      id: '4',
      name: 'Weekly Backup - 2024-01-13',
      type: 'automatic',
      size: '2.1 GB',
      status: 'failed',
      createdAt: '2024-01-13 02:00:00',
      duration: '0 minutes',
      description: 'Backup failed due to insufficient storage space'
    }
  ];

  const mockMaintenanceTasks: MaintenanceTask[] = [
    {
      id: '1',
      name: 'Database Optimization',
      description: 'Optimize database indexes and clean up temporary data',
      type: 'database',
      status: 'completed',
      lastRun: '2024-01-15 03:00:00',
      nextRun: '2024-01-16 03:00:00',
      frequency: 'Daily'
    },
    {
      id: '2',
      name: 'Log Cleanup',
      description: 'Archive old log files and clean up disk space',
      type: 'system',
      status: 'completed',
      lastRun: '2024-01-15 04:00:00',
      nextRun: '2024-01-22 04:00:00',
      frequency: 'Weekly'
    },
    {
      id: '3',
      name: 'Security Scan',
      description: 'Scan system for security vulnerabilities',
      type: 'security',
      status: 'pending',
      nextRun: '2024-01-16 01:00:00',
      frequency: 'Daily'
    },
    {
      id: '4',
      name: 'System Health Check',
      description: 'Check system resources and performance metrics',
      type: 'system',
      status: 'running',
      lastRun: '2024-01-15 10:00:00',
      nextRun: '2024-01-15 11:00:00',
      frequency: 'Hourly'
    }
  ];

  useEffect(() => {
    setBackups(mockBackups);
    setMaintenanceTasks(mockMaintenanceTasks);
  }, []);

  const handleCreateBackup = async () => {
    setBackupInProgress(true);
    setBackupProgress(0);
    setError(null);

    try {
      // Simulate backup progress
      const interval = setInterval(() => {
        setBackupProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setBackupInProgress(false);
            setSuccess('Backup created successfully');
            
            // Add new backup to list
            const newBackup: BackupRecord = {
              id: Date.now().toString(),
              name: `Manual Backup - ${new Date().toLocaleDateString()}`,
              type: 'manual',
              size: '2.5 GB',
              status: 'completed',
              createdAt: new Date().toISOString(),
              duration: '10 minutes',
              description: 'Manual backup created by user'
            };
            setBackups(prev => [newBackup, ...prev]);
            
            return 100;
          }
          return prev + 10;
        });
      }, 500);
    } catch (err) {
      setError('Failed to create backup');
      setBackupInProgress(false);
    }
  };

  const handleRestoreBackup = async (backupId: string) => {
    if (!confirm('Are you sure you want to restore this backup? This will overwrite current data.')) {
      return;
    }

    setLoading(true);
    try {
      // Simulate restore process
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSuccess('Backup restored successfully');
    } catch (err) {
      setError('Failed to restore backup');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteBackup = async (backupId: string) => {
    if (!confirm('Are you sure you want to delete this backup?')) {
      return;
    }

    setBackups(prev => prev.filter(backup => backup.id !== backupId));
    setSuccess('Backup deleted successfully');
  };

  const handleRunMaintenanceTask = async (taskId: string) => {
    setMaintenanceTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, status: 'running' } : task
    ));

    try {
      // Simulate task execution
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setMaintenanceTasks(prev => prev.map(task => 
        task.id === taskId ? { 
          ...task, 
          status: 'completed',
          lastRun: new Date().toISOString()
        } : task
      ));
      
      setSuccess('Maintenance task completed successfully');
    } catch (err) {
      setMaintenanceTasks(prev => prev.map(task => 
        task.id === taskId ? { ...task, status: 'failed' } : task
      ));
      setError('Maintenance task failed');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
      case 'healthy':
        return <CheckCircle size={16} className="text-green-500 dark:text-green-400" />;
      case 'failed':
        return <XCircle size={16} className="text-red-500 dark:text-red-400" />;
      case 'running':
      case 'in_progress':
        return <RefreshCw size={16} className="text-blue-500 dark:text-blue-400 animate-spin" />;
      case 'pending':
        return <Clock size={16} className="text-yellow-500 dark:text-yellow-400" />;
      default:
        return <AlertTriangle size={16} className="text-gray-500 dark:text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'healthy':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'failed':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'running':
      case 'in_progress':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'pending':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      default:
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'database':
        return <Database size={16} className="text-blue-500 dark:text-blue-400" />;
      case 'security':
        return <Shield size={16} className="text-red-500 dark:text-red-400" />;
      case 'system':
        return <Settings size={16} className="text-gray-500 dark:text-gray-400" />;
      default:
        return <Settings size={16} className="text-gray-500 dark:text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Backup & Maintenance</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage system backups, maintenance tasks, and system health
          </p>
        </div>
        <div className="flex space-x-3">
          {activeTab === 'backups' && (
            <button
              onClick={handleCreateBackup}
              disabled={backupInProgress}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
            >
              {backupInProgress ? (
                <RefreshCw size={16} className="animate-spin" />
              ) : (
                <Database size={16} />
              )}
              <span>{backupInProgress ? 'Creating...' : 'Create Backup'}</span>
            </button>
          )}
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle size={20} className="text-green-500 dark:text-green-400" />
            <span className="text-green-700 dark:text-green-300">{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle size={20} className="text-red-500 dark:text-red-400" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* Backup Progress */}
      {backupInProgress && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center space-x-3 mb-3">
            <RefreshCw size={20} className="text-blue-500 dark:text-blue-400 animate-spin" />
            <span className="text-blue-700 dark:text-blue-300 font-medium">Creating backup...</span>
          </div>
          <div className="bg-blue-200 dark:bg-blue-800 rounded-full h-2">
            <div
              className="bg-blue-500 dark:bg-blue-400 h-2 rounded-full transition-all duration-300"
              style={{ width: `${backupProgress}%` }}
            />
          </div>
          <div className="flex justify-between items-center mt-2">
            <span className="text-sm text-blue-600 dark:text-blue-400">
              {backupProgress}% complete
            </span>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'backups', label: 'Backups', icon: Database },
              { id: 'maintenance', label: 'Maintenance', icon: Settings },
              { id: 'health', label: 'System Health', icon: HardDrive }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === id
                    ? 'border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <Icon size={16} />
                <span>{label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'backups' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Backup History</h3>
              
              <div className="space-y-4">
                {backups.map(backup => (
                  <div key={backup.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(backup.status)}
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(backup.status)}`}>
                            {backup.status}
                          </span>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-gray-100">{backup.name}</h4>
                          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
                            <span className="flex items-center space-x-1">
                              <HardDrive size={12} />
                              <span>{backup.size}</span>
                            </span>
                            <span className="flex items-center space-x-1">
                              <Clock size={12} />
                              <span>{backup.duration}</span>
                            </span>
                            <span className="flex items-center space-x-1">
                              <Calendar size={12} />
                              <span>{new Date(backup.createdAt).toLocaleString()}</span>
                            </span>
                          </div>
                          {backup.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{backup.description}</p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleRestoreBackup(backup.id)}
                          disabled={backup.status !== 'completed' || loading}
                          className="p-2 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors group disabled:opacity-50"
                          title="Restore Backup"
                        >
                          <Upload size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400" />
                        </button>
                        <button
                          className="p-2 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors group"
                          title="Download Backup"
                        >
                          <Download size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-green-600 dark:group-hover:text-green-400" />
                        </button>
                        <button
                          onClick={() => handleDeleteBackup(backup.id)}
                          className="p-2 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors group"
                          title="Delete Backup"
                        >
                          <Trash2 size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'maintenance' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Maintenance Tasks</h3>

              <div className="space-y-4">
                {maintenanceTasks.map(task => (
                  <div key={task.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getTypeIcon(task.type)}
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="font-medium text-gray-900 dark:text-gray-100">{task.name}</h4>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(task.status)}`}>
                              {task.status}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{task.description}</p>
                          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-2">
                            <span>Frequency: {task.frequency}</span>
                            {task.lastRun && (
                              <span>Last run: {new Date(task.lastRun).toLocaleString()}</span>
                            )}
                            {task.nextRun && (
                              <span>Next run: {new Date(task.nextRun).toLocaleString()}</span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleRunMaintenanceTask(task.id)}
                          disabled={task.status === 'running'}
                          className="p-2 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors group disabled:opacity-50"
                          title="Run Task"
                        >
                          <Play size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400" />
                        </button>
                        <button
                          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors group"
                          title="View Details"
                        >
                          <Eye size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'health' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">System Health Overview</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Database size={20} className="text-blue-500 dark:text-blue-400" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">Database</h4>
                    </div>
                    {getStatusIcon(systemHealth.database.status)}
                  </div>
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className="font-medium">{systemHealth.database.status}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Last Check:</span>
                      <span>{systemHealth.database.lastCheck}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <HardDrive size={20} className="text-green-500 dark:text-green-400" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">Storage</h4>
                    </div>
                    {getStatusIcon(systemHealth.storage.status)}
                  </div>
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span>Usage:</span>
                      <span className="font-medium">{systemHealth.storage.usage}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Available:</span>
                      <span>{systemHealth.storage.available}</span>
                    </div>
                  </div>
                  <div className="mt-3">
                    <div className="bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div
                        className="bg-green-500 dark:bg-green-400 h-2 rounded-full"
                        style={{ width: systemHealth.storage.usage }}
                      />
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <RefreshCw size={20} className="text-purple-500 dark:text-purple-400" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">Memory</h4>
                    </div>
                    {getStatusIcon(systemHealth.memory.status)}
                  </div>
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span>Usage:</span>
                      <span className="font-medium">{systemHealth.memory.usage}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Available:</span>
                      <span>{systemHealth.memory.available}</span>
                    </div>
                  </div>
                  <div className="mt-3">
                    <div className="bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div
                        className="bg-purple-500 dark:bg-purple-400 h-2 rounded-full"
                        style={{ width: systemHealth.memory.usage }}
                      />
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Settings size={20} className="text-orange-500 dark:text-orange-400" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">CPU</h4>
                    </div>
                    {getStatusIcon(systemHealth.cpu.status)}
                  </div>
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span>Usage:</span>
                      <span className="font-medium">{systemHealth.cpu.usage}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Load:</span>
                      <span>{systemHealth.cpu.load}</span>
                    </div>
                  </div>
                  <div className="mt-3">
                    <div className="bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div
                        className="bg-orange-500 dark:bg-orange-400 h-2 rounded-full"
                        style={{ width: systemHealth.cpu.usage }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
