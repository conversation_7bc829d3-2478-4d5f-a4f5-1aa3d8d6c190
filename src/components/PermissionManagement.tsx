import React, { useState, useEffect } from 'react';
import {
  Key,
  Shield,
  Users,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Check,
  X,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  Loader,
  Eye,
  Settings,
  Lock,
  Un<PERSON>,
  <PERSON>r<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  RefreshCw
} from 'lucide-react';
import { adminAPI } from '../services/apiService';

interface Permission {
  _id: string;
  module: string;
  action: string;
  resource: string;
  description: string;
  category: string;
  isSystemPermission: boolean;
}

interface Role {
  _id: string;
  name: string;
  description: string;
  level: number;
  isSystemRole: boolean;
  defaultPermissions: Permission[];
  userCount?: number;
}

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: Role;
  permissions: Permission[];
  isActive: boolean;
}

export function PermissionManagement() {
  const [activeTab, setActiveTab] = useState('permissions');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Data states
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [permissionCategories, setPermissionCategories] = useState<any>({});
  const [roles, setRoles] = useState<Role[]>([]);
  const [users, setUsers] = useState<User[]>([]);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [moduleFilter, setModuleFilter] = useState('all');

  // Modal states
  const [showRolePermissionModal, setShowRolePermissionModal] = useState(false);
  const [showUserPermissionModal, setShowUserPermissionModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchPermissions(),
        fetchRoles(),
        fetchUsers()
      ]);
    } catch (err) {
      setError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const fetchPermissions = async () => {
    try {
      const [categoriesResponse, allPermissionsResponse] = await Promise.all([
        adminAPI.getPermissionCategories(),
        adminAPI.getAllPermissions()
      ]);

      if (categoriesResponse.success) {
        setPermissionCategories(categoriesResponse.data);
      }

      if (allPermissionsResponse.success) {
        setPermissions(allPermissionsResponse.data.permissions);
      }
    } catch (err) {
      console.error('Error fetching permissions:', err);
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await adminAPI.getRoles();
      if (response.success) {
        setRoles(response.data);
      }
    } catch (err) {
      console.error('Error fetching roles:', err);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await adminAPI.getUsers();
      if (response.success) {
        setUsers(response.data);
      }
    } catch (err) {
      console.error('Error fetching users:', err);
    }
  };

  const handleAssignPermissionsToRole = async (roleId: string, permissionIds: string[]) => {
    setLoading(true);
    try {
      const response = await adminAPI.assignPermissionsToRole(roleId, permissionIds);
      if (response.success) {
        setSuccess('Permissions assigned to role successfully');
        await fetchRoles();
        setShowRolePermissionModal(false);
        setSelectedRole(null);
        setSelectedPermissions([]);
      } else {
        setError(response.error || 'Failed to assign permissions');
      }
    } catch (err) {
      setError('Failed to assign permissions');
    } finally {
      setLoading(false);
    }
  };

  const handleAssignRoleToUser = async (userId: string, roleId: string) => {
    setLoading(true);
    try {
      const response = await adminAPI.assignRoleToUser(userId, roleId);
      if (response.success) {
        setSuccess('Role assigned to user successfully');
        await fetchUsers();
      } else {
        setError(response.error || 'Failed to assign role');
      }
    } catch (err) {
      setError('Failed to assign role');
    } finally {
      setLoading(false);
    }
  };

  const openRolePermissionModal = (role: Role) => {
    setSelectedRole(role);
    setSelectedPermissions(role.defaultPermissions.map(p => p._id));
    setShowRolePermissionModal(true);
  };

  const openUserPermissionModal = (user: User) => {
    setSelectedUser(user);
    setSelectedPermissions(user.permissions.map(p => p._id));
    setShowUserPermissionModal(true);
  };

  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = searchTerm === '' || 
      permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.module.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.action.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = categoryFilter === 'all' || permission.category === categoryFilter;
    const matchesModule = moduleFilter === 'all' || permission.module === moduleFilter;

    return matchesSearch && matchesCategory && matchesModule;
  });

  const getPermissionKey = (permission: Permission) => {
    return `${permission.module}:${permission.action}:${permission.resource}`;
  };

  const getModules = () => {
    return [...new Set(permissions.map(p => p.module))];
  };

  const getCategories = () => {
    return [...new Set(permissions.map(p => p.category))];
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Permission Management</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage permissions for roles and users with granular control
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={fetchAllData}
            disabled={loading}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle size={20} className="text-green-500 dark:text-green-400" />
            <span className="text-green-700 dark:text-green-300">{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle size={20} className="text-red-500 dark:text-red-400" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'permissions', label: 'All Permissions', icon: Key },
              { id: 'role-permissions', label: 'Role Permissions', icon: Shield },
              { id: 'user-permissions', label: 'User Permissions', icon: Users }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === id
                    ? 'border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <Icon size={16} />
                <span>{label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'permissions' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">System Permissions</h3>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {filteredPermissions.length} of {permissions.length} permissions
                </div>
              </div>

              {/* Filters */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="relative">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
                  <input
                    type="text"
                    placeholder="Search permissions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                </div>

                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Categories</option>
                  {getCategories().map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>

                <select
                  value={moduleFilter}
                  onChange={(e) => setModuleFilter(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Modules</option>
                  {getModules().map(module => (
                    <option key={module} value={module}>{module}</option>
                  ))}
                </select>
              </div>

              {/* Permissions Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredPermissions.map(permission => (
                  <div key={permission._id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Key size={16} className="text-blue-500 dark:text-blue-400" />
                        <span className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                          {getPermissionKey(permission)}
                        </span>
                      </div>
                      {permission.isSystemPermission && (
                        <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs">
                          System
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{permission.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-2 py-1 rounded-full text-xs">
                        {permission.category}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-500">
                        {permission.module}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'role-permissions' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Role Permission Management</h3>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {roles.length} roles configured
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {roles.map(role => (
                  <div key={role._id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 rounded-full flex items-center justify-center">
                          <Shield size={18} className="text-white" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-gray-100">{role.name}</h4>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Level {role.level}</p>
                        </div>
                      </div>
                      {role.isSystemRole && (
                        <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs">
                          System
                        </span>
                      )}
                    </div>

                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">{role.description}</p>

                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Permissions ({role.defaultPermissions?.length || 0})
                        </span>
                        <button
                          onClick={() => openRolePermissionModal(role)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
                        >
                          Manage
                        </button>
                      </div>

                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {(role.defaultPermissions || []).slice(0, 5).map((permission, index) => (
                          <div key={index} className="flex items-center space-x-2 text-xs">
                            <Key size={12} className="text-gray-400 dark:text-gray-500" />
                            <span className="text-gray-600 dark:text-gray-400">
                              {permission.module}:{permission.action}:{permission.resource}
                            </span>
                          </div>
                        ))}
                        {(role.defaultPermissions?.length || 0) > 5 && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 pl-5">
                            +{(role.defaultPermissions?.length || 0) - 5} more permissions
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {users.filter(u => u.role?._id === role._id).length} users
                      </span>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => openRolePermissionModal(role)}
                          className="p-2 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors group"
                          title="Edit Permissions"
                        >
                          <Edit size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400" />
                        </button>
                        <button
                          className="p-2 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors group"
                          title="View Details"
                        >
                          <Eye size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-green-600 dark:group-hover:text-green-400" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'user-permissions' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">User Permission Management</h3>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {users.length} users configured
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">User</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Current Role</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Permissions</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Status</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {users.map(user => (
                        <tr key={user._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 dark:from-purple-600 dark:to-purple-700 rounded-full flex items-center justify-center">
                                <span className="text-white text-sm font-medium">
                                  {user.firstName[0]}{user.lastName[0]}
                                </span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 dark:text-gray-100">
                                  {user.firstName} {user.lastName}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-2">
                              <Shield size={16} className="text-blue-500 dark:text-blue-400" />
                              <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-sm">
                                {user.role?.name || 'No Role'}
                              </span>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-2">
                              <Key size={16} className="text-green-500 dark:text-green-400" />
                              <span className="text-sm text-gray-900 dark:text-gray-100">
                                {user.permissions?.length || 0} permissions
                              </span>
                              <button
                                onClick={() => openUserPermissionModal(user)}
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"
                              >
                                View
                              </button>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              user.isActive
                                ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                                : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                            }`}>
                              {user.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex space-x-2">
                              <select
                                value={user.role?._id || ''}
                                onChange={(e) => {
                                  if (e.target.value && e.target.value !== user.role?._id) {
                                    handleAssignRoleToUser(user._id, e.target.value);
                                  }
                                }}
                                className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                              >
                                <option value="">Select Role</option>
                                {roles.map(role => (
                                  <option key={role._id} value={role._id}>{role.name}</option>
                                ))}
                              </select>
                              <button
                                onClick={() => openUserPermissionModal(user)}
                                className="p-2 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors group"
                                title="Manage Permissions"
                              >
                                <Settings size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Role Permission Assignment Modal */}
      {showRolePermissionModal && selectedRole && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Manage Permissions for {selectedRole.name}
                </h2>
                <button
                  onClick={() => {
                    setShowRolePermissionModal(false);
                    setSelectedRole(null);
                    setSelectedPermissions([]);
                  }}
                  className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X size={24} />
                </button>
              </div>

              <div className="mb-6">
                <div className="flex items-center justify-between">
                  <p className="text-gray-600 dark:text-gray-400">{selectedRole.description}</p>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {selectedPermissions.length} of {permissions.length} permissions selected
                  </span>
                </div>
              </div>

              <div className="space-y-4 max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                {Object.entries(permissionCategories).map(([category, categoryPermissions]: [string, any]) => (
                  <div key={category} className="space-y-2">
                    <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-600 pb-2">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">{category}</h4>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            const categoryPermissionIds = categoryPermissions.map((p: Permission) => p._id);
                            setSelectedPermissions(prev => [
                              ...prev.filter(id => !categoryPermissionIds.includes(id)),
                              ...categoryPermissionIds
                            ]);
                          }}
                          className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                        >
                          Select All
                        </button>
                        <button
                          onClick={() => {
                            const categoryPermissionIds = categoryPermissions.map((p: Permission) => p._id);
                            setSelectedPermissions(prev => prev.filter(id => !categoryPermissionIds.includes(id)));
                          }}
                          className="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                        >
                          Deselect All
                        </button>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {categoryPermissions.map((permission: Permission) => (
                        <label key={permission._id} className="flex items-center space-x-2 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
                          <input
                            type="checkbox"
                            checked={selectedPermissions.includes(permission._id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedPermissions(prev => [...prev, permission._id]);
                              } else {
                                setSelectedPermissions(prev => prev.filter(id => id !== permission._id));
                              }
                            }}
                            className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
                          />
                          <div className="flex-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {getPermissionKey(permission)}
                            </span>
                            <p className="text-xs text-gray-500 dark:text-gray-400">{permission.description}</p>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => {
                    setShowRolePermissionModal(false);
                    setSelectedRole(null);
                    setSelectedPermissions([]);
                  }}
                  className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleAssignPermissionsToRole(selectedRole._id, selectedPermissions)}
                  disabled={loading}
                  className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-blue-300 dark:disabled:bg-blue-700 flex items-center space-x-2"
                >
                  {loading && <Loader className="animate-spin" size={16} />}
                  <span>Update Permissions</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* User Permission Modal */}
      {showUserPermissionModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Permissions for {selectedUser.firstName} {selectedUser.lastName}
                </h2>
                <button
                  onClick={() => {
                    setShowUserPermissionModal(false);
                    setSelectedUser(null);
                    setSelectedPermissions([]);
                  }}
                  className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X size={24} />
                </button>
              </div>

              <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Shield size={16} className="text-blue-500 dark:text-blue-400" />
                  <span className="font-medium text-blue-900 dark:text-blue-100">Current Role: {selectedUser.role?.name || 'No Role'}</span>
                </div>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  This user inherits {selectedUser.role?.defaultPermissions?.length || 0} permissions from their role.
                  You can view all effective permissions below.
                </p>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Effective Permissions</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                  {selectedUser.permissions.map(permission => (
                    <div key={permission._id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                      <div className="flex items-center space-x-2 mb-1">
                        <Key size={14} className="text-green-500 dark:text-green-400" />
                        <span className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                          {getPermissionKey(permission)}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">{permission.description}</p>
                      <span className="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-2 py-1 rounded-full text-xs">
                        {permission.category}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => {
                    setShowUserPermissionModal(false);
                    setSelectedUser(null);
                    setSelectedPermissions([]);
                  }}
                  className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
