import React, { useState, useEffect } from 'react';
import {
  Shield,
  Users,
  Key,
  AlertCircle,
  CheckCircle,
  Lock,
  RefreshCw,
  FileText,
  Database,
  UserPlus,
  Edit,
  Trash2,
  X,
  Loader,
  Search,
  Download,
  Plus,
  Settings
} from 'lucide-react';
import { adminAPI } from '../services/apiService';
import { useAuth } from '../context/AuthContext';
import { SuperAdminPanel } from './SuperAdminPanel';
import { EnhancedUserManagement } from './EnhancedUserManagement';
import { AdvancedAuditSystem } from './AdvancedAuditSystem';
import { PermissionManagement } from './PermissionManagement';
import { LoadingSpinner, SkeletonStats, SkeletonTable } from './LoadingSpinner';
import { SystemSettings } from './SystemSettings';
import { BackupMaintenance } from './BackupMaintenance';

// Available permissions for role management
const availablePermissions = [
  'USER_MANAGEMENT',
  'PATIENT_MANAGEMENT',
  'APPOINTMENT_MANAGEMENT',
  'BILLING_MANAGEMENT',
  'REPORT_GENERATION',
  'FACILITY_MANAGEMENT',
  'LABORATORY_MANAGEMENT',
  'PHARMACY_MANAGEMENT',
  'SYSTEM_ADMINISTRATION',
  'AUDIT_LOG_ACCESS',
  'BACKUP_RESTORE',
  'NOTIFICATION_MANAGEMENT'
];

export function Administration() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('users');

  // State for data
  const [users, setUsers] = useState<any[]>([]);
  const [roles, setRoles] = useState<any[]>([]);
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const [systemStats, setSystemStats] = useState<any>(null);

  // Loading and error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Modal states
  const [showUserModal, setShowUserModal] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [selectedRole, setSelectedRole] = useState<any>(null);

  // Form states
  const [userFormData, setUserFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    role: '',
    department: '',
    phone: '',
    employeeId: '',
    position: '',
    specialization: '',
    licenseNumber: ''
  });

  const [roleFormData, setRoleFormData] = useState({
    name: '',
    description: '',
    permissions: [] as string[]
  });

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Form loading
  const [formLoading, setFormLoading] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    fetchUsers();
    fetchRoles();
    fetchAuditLogs();
    fetchSystemStats();
  }, [currentPage, searchTerm, roleFilter, statusFilter]);

  // Add refresh function
  const refreshAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchUsers(),
        fetchRoles(),
        fetchAuditLogs(),
        fetchSystemStats()
      ]);
    } catch (err) {
      console.error('Error refreshing data:', err);
      setError('Failed to refresh data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        limit: 20
      };

      if (searchTerm) params.search = searchTerm;
      if (roleFilter !== 'all') params.role = roleFilter;
      if (statusFilter !== 'all') params.status = statusFilter;

      const response = await adminAPI.getUsers(params);
      if (response.success) {
        setUsers(response.data);
        setTotalPages(response.pagination.pages);
        setError(null);
      } else {
        setError('Failed to fetch users');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await adminAPI.getRoles();
      if (response.success) {
        setRoles(response.data);
        setError(null);
      }
    } catch (err) {
      console.error('Error fetching roles:', err);
      if (err instanceof Error && err.message?.includes('Insufficient permissions')) {
        setError('Access denied. Please log out and log back in to refresh your permissions.');
      } else {
        setError(err instanceof Error ? err.message : 'Failed to fetch roles');
      }
    }
  };

  const fetchAuditLogs = async () => {
    try {
      const response = await adminAPI.getAuditLogs({ limit: 50 });
      if (response.success) {
        setAuditLogs(response.data);
        setError(null);
      }
    } catch (err) {
      console.error('Error fetching audit logs:', err);
      if (err instanceof Error && err.message?.includes('Insufficient permissions')) {
        setError('Access denied. Please log out and log back in to refresh your permissions.');
      } else {
        setError(err instanceof Error ? err.message : 'Failed to fetch audit logs');
      }
    }
  };

  const fetchSystemStats = async () => {
    try {
      const response = await adminAPI.getSystemStats();
      if (response.success) {
        setSystemStats(response.data);
        setError(null);
      }
    } catch (err) {
      console.error('Error fetching system stats:', err);
      if (err instanceof Error && err.message?.includes('Insufficient permissions')) {
        setError('Access denied. Please log out and log back in to refresh your permissions.');
      } else {
        setError(err instanceof Error ? err.message : 'Failed to fetch system stats');
      }
    }
  };

  // User management handlers
  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setFormLoading(true);
      const response = await adminAPI.createUser(userFormData);
      if (response.success) {
        await fetchUsers();
        await fetchSystemStats();
        setShowUserModal(false);
        resetUserForm();
        setSuccess('User created successfully');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(response.error || 'Failed to create user');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create user');
    } finally {
      setFormLoading(false);
    }
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUser) return;

    try {
      setFormLoading(true);
      const response = await adminAPI.updateUser(selectedUser._id, userFormData);
      if (response.success) {
        await fetchUsers();
        setShowUserModal(false);
        resetUserForm();
        setSelectedUser(null);
        setSuccess('User updated successfully');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(response.error || 'Failed to update user');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update user');
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      const response = await adminAPI.deleteUser(userId);
      if (response.success) {
        await fetchUsers();
        await fetchSystemStats();
        setSuccess('User deleted successfully');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(response.error || 'Failed to delete user');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete user');
    }
  };

  const handleToggleUserStatus = async (userId: string) => {
    try {
      const response = await adminAPI.toggleUserStatus(userId);
      if (response.success) {
        await fetchUsers();
        setSuccess('User status updated successfully');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(response.error || 'Failed to update user status');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update user status');
    }
  };

  const handleResetPassword = async (userId: string) => {
    const newPassword = prompt('Enter new password for user:');
    if (!newPassword) return;

    try {
      const response = await adminAPI.resetUserPassword(userId, newPassword);
      if (response.success) {
        setSuccess('Password reset successfully');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(response.error || 'Failed to reset password');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reset password');
    }
  };

  const resetUserForm = () => {
    setUserFormData({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      role: '',
      department: '',
      phone: '',
      employeeId: '',
      position: '',
      specialization: '',
      licenseNumber: ''
    });
  };

  const openUserModal = (user?: any) => {
    if (user) {
      setSelectedUser(user);
      setUserFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        password: '',
        role: user.role?._id || '',
        department: user.department || '',
        phone: user.phone || '',
        employeeId: user.employeeId || '',
        position: user.position || '',
        specialization: user.specialization || '',
        licenseNumber: user.licenseNumber || ''
      });
    } else {
      setSelectedUser(null);
      resetUserForm();
    }
    setShowUserModal(true);
  };

  // Role management handlers
  const handleCreateRole = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setFormLoading(true);
      const response = await adminAPI.createRole(roleFormData);
      if (response.success) {
        await fetchRoles();
        setShowRoleModal(false);
        resetRoleForm();
        setSuccess('Role created successfully');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(response.error || 'Failed to create role');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create role');
    } finally {
      setFormLoading(false);
    }
  };

  const handleUpdateRole = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedRole) return;

    try {
      setFormLoading(true);
      const response = await adminAPI.updateRole(selectedRole._id, roleFormData);
      if (response.success) {
        await fetchRoles();
        setShowRoleModal(false);
        resetRoleForm();
        setSelectedRole(null);
        setSuccess('Role updated successfully');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(response.error || 'Failed to update role');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update role');
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    if (!confirm('Are you sure you want to delete this role?')) return;

    try {
      const response = await adminAPI.deleteRole(roleId);
      if (response.success) {
        await fetchRoles();
        setSuccess('Role deleted successfully');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(response.error || 'Failed to delete role');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete role');
    }
  };

  const resetRoleForm = () => {
    setRoleFormData({
      name: '',
      description: '',
      permissions: []
    });
  };

  const openRoleModal = (role?: any) => {
    if (role) {
      setSelectedRole(role);
      setRoleFormData({
        name: role.name || '',
        description: role.description || '',
        permissions: role.permissions || role.defaultPermissions || []
      });
    } else {
      setSelectedRole(null);
      resetRoleForm();
    }
    setShowRoleModal(true);
  };

  // Helper functions for styling
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'Administrator': return 'bg-purple-100 text-purple-800';
      case 'Doctor': return 'bg-blue-100 text-blue-800';
      case 'Nurse': return 'bg-green-100 text-green-800';
      case 'Receptionist': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Inactive': return 'bg-red-100 text-red-800';
      case 'Suspended': return 'bg-yellow-100 text-yellow-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Success': return 'bg-green-100 text-green-800';
      case 'Failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 rounded-xl flex items-center justify-center shadow-lg">
              <Settings size={28} className="text-white" />
            </div>
            <span>Vaidya Administration</span>
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2 text-lg">
            Intelligent Care, Ancient Wisdom - Complete System Management
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-900/20 px-4 py-3 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 dark:bg-green-400 rounded-full animate-pulse shadow-sm"></div>
              <div>
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">System Status</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">All services operational</div>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={refreshAllData}
              disabled={loading}
              className="bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 disabled:opacity-50 shadow-md hover:shadow-lg"
            >
              <Loader size={18} className={loading ? 'animate-spin' : 'hidden'} />
              <RefreshCw size={18} className={loading ? 'hidden' : 'block'} />
              <span>Refresh</span>
            </button>
            <button
              onClick={() => openUserModal()}
              className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 shadow-md hover:shadow-lg"
            >
              <UserPlus size={18} />
              <span>Add User</span>
            </button>
            <button
              onClick={() => {/* TODO: Implement export functionality */}}
              className="bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 shadow-md hover:shadow-lg"
            >
              <Download size={18} />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle size={20} className="text-green-500 dark:text-green-400" />
            <span className="text-green-700 dark:text-green-300">{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AlertCircle size={20} className="text-red-500 dark:text-red-400" />
              <span className="text-red-700 dark:text-red-300">{error}</span>
            </div>
            {error.includes('log out and log back in') && (
              <button
                onClick={() => {
                  localStorage.removeItem('token');
                  localStorage.removeItem('refreshToken');
                  window.location.reload();
                }}
                className="bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 text-white px-3 py-1 rounded text-sm"
              >
                Logout & Refresh
              </button>
            )}
          </div>
        </div>
      )}

      {/* Enhanced Admin Overview Cards */}
      {loading ? (
        <SkeletonStats count={4} />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl shadow-lg dark:shadow-gray-900/20 p-6 border border-blue-200 dark:border-blue-800/50 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400 mb-1">Total Users</p>
              <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">
                {systemStats?.totalUsers || users.length}
              </p>
              <p className="text-xs text-blue-500 dark:text-blue-400 mt-1">
                +{Math.floor(Math.random() * 5)} this week
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-500 dark:bg-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <Users size={24} className="text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl shadow-lg dark:shadow-gray-900/20 p-6 border border-green-200 dark:border-green-800/50 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600 dark:text-green-400 mb-1">Active Users</p>
              <p className="text-3xl font-bold text-green-900 dark:text-green-100">
                {systemStats?.activeUsers || users.filter(user => user.status === 'Active').length}
              </p>
              <p className="text-xs text-green-500 dark:text-green-400 mt-1">
                {Math.round((users.filter(user => user.status === 'Active').length / users.length) * 100 || 0)}% active
              </p>
            </div>
            <div className="w-12 h-12 bg-green-500 dark:bg-green-600 rounded-xl flex items-center justify-center shadow-lg">
              <CheckCircle size={24} className="text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-xl shadow-lg dark:shadow-gray-900/20 p-6 border border-red-200 dark:border-red-800/50 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-red-600 dark:text-red-400 mb-1">Failed Logins</p>
              <p className="text-3xl font-bold text-red-900 dark:text-red-100">
                {auditLogs.filter(log => log.status === 'Failed').length}
              </p>
              <p className="text-xs text-red-500 dark:text-red-400 mt-1">
                Last 24 hours
              </p>
            </div>
            <div className="w-12 h-12 bg-red-500 dark:bg-red-600 rounded-xl flex items-center justify-center shadow-lg">
              <AlertCircle size={24} className="text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl shadow-lg dark:shadow-gray-900/20 p-6 border border-purple-200 dark:border-purple-800/50 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600 dark:text-purple-400 mb-1">System Health</p>
              <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">99.2%</p>
              <p className="text-xs text-purple-500 dark:text-purple-400 mt-1">
                All services operational
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-500 dark:bg-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <Shield size={24} className="text-white" />
            </div>
          </div>
        </div>
        </div>
      )}

      {/* Enhanced Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg dark:shadow-gray-900/20 border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800">
          <nav className="flex space-x-1 px-6 py-2 overflow-x-auto">
            <button
              onClick={() => setActiveTab('users')}
              className={`px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 flex items-center space-x-2 ${
                activeTab === 'users'
                  ? 'bg-blue-500 dark:bg-blue-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
              }`}
            >
              <Users size={16} />
              <span>User Management</span>
            </button>
            <button
              onClick={() => setActiveTab('enhanced-users')}
              className={`px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 flex items-center space-x-2 ${
                activeTab === 'enhanced-users'
                  ? 'bg-purple-500 dark:bg-purple-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20'
              }`}
            >
              <UserPlus size={16} />
              <span>Enhanced Users</span>
            </button>
            <button
              onClick={() => setActiveTab('permissions')}
              className={`px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 flex items-center space-x-2 ${
                activeTab === 'permissions'
                  ? 'bg-green-500 dark:bg-green-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20'
              }`}
            >
              <Key size={16} />
              <span>Permissions</span>
            </button>
            <button
              onClick={() => setActiveTab('roles')}
              className={`px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 flex items-center space-x-2 ${
                activeTab === 'roles'
                  ? 'bg-orange-500 dark:bg-orange-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:text-orange-600 dark:hover:text-orange-400 hover:bg-orange-50 dark:hover:bg-orange-900/20'
              }`}
            >
              <Shield size={16} />
              <span>Role Management</span>
            </button>
            <button
              onClick={() => setActiveTab('audit')}
              className={`px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 flex items-center space-x-2 ${
                activeTab === 'audit'
                  ? 'bg-indigo-500 dark:bg-indigo-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20'
              }`}
            >
              <FileText size={16} />
              <span>Audit Logs</span>
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 flex items-center space-x-2 ${
                activeTab === 'settings'
                  ? 'bg-gray-500 dark:bg-gray-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              <Settings size={16} />
              <span>System Settings</span>
            </button>
            <button
              onClick={() => setActiveTab('compliance')}
              className={`px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 flex items-center space-x-2 ${
                activeTab === 'compliance'
                  ? 'bg-teal-500 dark:bg-teal-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400 hover:bg-teal-50 dark:hover:bg-teal-900/20'
              }`}
            >
              <CheckCircle size={16} />
              <span>Compliance</span>
            </button>
            <button
              onClick={() => setActiveTab('backup')}
              className={`px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 flex items-center space-x-2 ${
                activeTab === 'backup'
                  ? 'bg-emerald-500 dark:bg-emerald-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:text-emerald-600 dark:hover:text-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20'
              }`}
            >
              <Database size={16} />
              <span>Backup & Maintenance</span>
            </button>
            {user?.role?.level && user.role.level >= 10 && (
              <button
                onClick={() => setActiveTab('superadmin')}
                className={`px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 flex items-center space-x-2 ${
                  activeTab === 'superadmin'
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 dark:from-purple-600 dark:to-pink-600 text-white shadow-md'
                    : 'text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20'
                }`}
                >
                <Shield size={16} />
                <span>Super Admin</span>
              </button>
            )}
          </nav>
        </div>

        <div className="p-8 bg-gray-50 dark:bg-gray-900/50">
          {activeTab === 'users' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">User Accounts</h2>
                <button
                  onClick={() => openUserModal()}
                  className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
                >
                  <UserPlus size={16} />
                  <span>Add User</span>
                </button>
              </div>

              {/* Search and Filter */}
              <div className="flex items-center space-x-4">
                <div className="relative flex-1">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
                  <input
                    type="text"
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
                <select
                  value={roleFilter}
                  onChange={(e) => setRoleFilter(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Roles</option>
                  {roles.map((role) => (
                    <option key={role._id} value={role._id}>{role.name}</option>
                  ))}
                </select>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Status</option>
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                </select>
              </div>

              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader className="animate-spin text-blue-500 dark:text-blue-400" size={24} />
                  <span className="ml-2 text-gray-500 dark:text-gray-400">Loading users...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 border border-gray-200 dark:border-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">User</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Role</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Department</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Status</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Last Login</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {users.map((user) => (
                        <tr key={user._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 rounded-full flex items-center justify-center">
                                <Users size={18} className="text-white" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 dark:text-gray-100">
                                  {user.firstName} {user.lastName}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <span className={`px-3 py-1 text-xs font-medium rounded-full ${getRoleColor(user.role?.name || 'User')}`}>
                              {user.role?.name || 'User'}
                            </span>
                          </td>
                          <td className="py-4 px-6 text-sm text-gray-900 dark:text-gray-100">{user.department || 'N/A'}</td>
                          <td className="py-4 px-6">
                            <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(user.status)}`}>
                              {user.status}
                            </span>
                          </td>
                          <td className="py-4 px-6 text-sm text-gray-900 dark:text-gray-100">
                            {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => openUserModal(user)}
                                className="p-2 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors group"
                                title="Edit User"
                              >
                                <Edit size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400" />
                              </button>
                              <button
                                onClick={() => handleResetPassword(user._id)}
                                className="p-2 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 rounded-lg transition-colors group"
                                title="Reset Password"
                              >
                                <Key size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-yellow-600 dark:group-hover:text-yellow-400" />
                              </button>
                              <button
                                onClick={() => handleToggleUserStatus(user._id)}
                                className="p-2 hover:bg-orange-100 dark:hover:bg-orange-900/30 rounded-lg transition-colors group"
                                title="Toggle Status"
                              >
                                <Lock size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-orange-600 dark:group-hover:text-orange-400" />
                              </button>
                              <button
                                onClick={() => handleDeleteUser(user._id)}
                                className="p-2 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors group"
                                title="Delete User"
                              >
                                <Trash2 size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    Previous
                  </button>

                  <span className="px-4 py-2 text-sm text-gray-600">
                    Page {currentPage} of {totalPages}
                  </span>

                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          )}

          {activeTab === 'roles' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Role Management</h2>
                <button
                  onClick={() => setShowRoleModal(true)}
                  className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
                >
                  <Plus size={16} />
                  <span>Add Role</span>
                </button>
              </div>

              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader className="animate-spin text-blue-500 dark:text-blue-400" size={24} />
                  <span className="ml-2 text-gray-500 dark:text-gray-400">Loading roles...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 border border-gray-200 dark:border-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Role Name</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Description</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Permissions</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Users</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {roles.map((role) => (
                        <tr key={role._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 dark:from-purple-600 dark:to-purple-700 rounded-full flex items-center justify-center">
                                <Shield size={18} className="text-white" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 dark:text-gray-100">{role.name}</div>
                                {role.isSystemRole && (
                                  <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded-full inline-block mt-1">
                                    System Role
                                  </div>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6 text-sm text-gray-900 dark:text-gray-100 max-w-xs">
                            <div className="truncate">{role.description}</div>
                          </td>
                          <td className="py-4 px-6 text-sm text-gray-900">
                            <div className="flex flex-wrap gap-1">
                              {(role.permissions || role.defaultPermissions || []).slice(0, 3).map((permission: any, index: number) => (
                                <span key={typeof permission === 'object' ? permission._id || index : permission}  className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">
                                  {typeof permission === 'string' ? permission.replace('_', ' ') : (permission.action || 'Permission')}
                                </span>
                              ))}
                              {(role.permissions || role.defaultPermissions || []).length > 3 && (
                                <span className="px-2 py-1 text-xs bg-gray-200 text-gray-600 rounded-full">
                                  +{(role.permissions || role.defaultPermissions || []).length - 3} more
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="py-4 px-6 text-sm text-gray-900">
                            {users.filter(user => user.role?._id === role?._id).length} users
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => openRoleModal(role)}
                                className="p-2 hover:bg-blue-100 rounded-lg transition-colors group"
                                title="Edit Role"
                              >
                                <Edit size={16} className="text-gray-500 group-hover:text-blue-600" />
                              </button>
                              {!role.isSystemRole && (
                                <button
                                  onClick={() => handleDeleteRole(role._id)}
                                  className="p-2 hover:bg-red-100 rounded-lg transition-colors group"
                                  title="Delete Role"
                                >
                                  <Trash2 size={16} className="text-gray-500 group-hover:text-red-600" />
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {activeTab === 'audit' && (
            <AdvancedAuditSystem />
          )}

          {activeTab === 'settings' && (
            <SystemSettings />
          )}

          {activeTab === 'compliance' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Compliance & Regulations</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Shield size={16} />
                  <span>Healthcare Standards</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <CheckCircle size={20} className="text-green-500" />
                    <h3 className="font-medium text-green-900">HIPAA Compliance</h3>
                  </div>
                  <p className="text-sm text-green-700">
                    All patient data is encrypted and access is logged. Last audit: Jan 2024
                  </p>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <CheckCircle size={20} className="text-blue-500" />
                    <h3 className="font-medium text-blue-900">HL7 FHIR</h3>
                  </div>
                  <p className="text-sm text-blue-700">
                    Data exchange follows HL7 FHIR standards for interoperability
                  </p>
                </div>

                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <CheckCircle size={20} className="text-purple-500" />
                    <h3 className="font-medium text-purple-900">GDPR Compliance</h3>
                  </div>
                  <p className="text-sm text-purple-700">
                    Patient consent management and data portability implemented
                  </p>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <AlertCircle size={20} className="text-yellow-500" />
                    <h3 className="font-medium text-yellow-900">NABH Standards</h3>
                  </div>
                  <p className="text-sm text-yellow-700">
                    Quality standards implementation in progress - 85% complete
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'enhanced-users' && (
            <EnhancedUserManagement />
          )}

          {activeTab === 'permissions' && (
            <PermissionManagement />
          )}

          {activeTab === 'backup' && (
            <BackupMaintenance />
          )}

          {activeTab === 'superadmin' && (
            <SuperAdminPanel />
          )}
        </div>
      </div>

      {/* User Creation/Edit Modal */}
      {showUserModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {selectedUser ? 'Edit User' : 'Create New User'}
                </h2>
                <button
                  onClick={() => {
                    setShowUserModal(false);
                    setSelectedUser(null);
                    resetUserForm();
                  }}
                  className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X size={24} />
                </button>
              </div>

              <form onSubmit={selectedUser ? handleUpdateUser : handleCreateUser} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      value={userFormData.firstName}
                      onChange={(e) => setUserFormData(prev => ({ ...prev, firstName: e.target.value }))}
                      required
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      value={userFormData.lastName}
                      onChange={(e) => setUserFormData(prev => ({ ...prev, lastName: e.target.value }))}
                      required
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email *
                    </label>
                    <input
                      type="email"
                      value={userFormData.email}
                      onChange={(e) => setUserFormData(prev => ({ ...prev, email: e.target.value }))}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  {!selectedUser && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Password *
                      </label>
                      <input
                        type="password"
                        value={userFormData.password}
                        onChange={(e) => setUserFormData(prev => ({ ...prev, password: e.target.value }))}
                        required={!selectedUser}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Role *
                    </label>
                    <select
                      value={userFormData.role}
                      onChange={(e) => setUserFormData(prev => ({ ...prev, role: e.target.value }))}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select Role</option>
                      {roles.map((role) => (
                        <option key={role._id} value={role._id}>{role.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Department
                    </label>
                    <select
                      value={userFormData.department}
                      onChange={(e) => setUserFormData(prev => ({ ...prev, department: e.target.value }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select Department</option>
                      <option value="Emergency">Emergency</option>
                      <option value="Cardiology">Cardiology</option>
                      <option value="Neurology">Neurology</option>
                      <option value="Orthopedics">Orthopedics</option>
                      <option value="Pediatrics">Pediatrics</option>
                      <option value="General Medicine">General Medicine</option>
                      <option value="Surgery">Surgery</option>
                      <option value="Radiology">Radiology</option>
                      <option value="Laboratory">Laboratory</option>
                      <option value="Pharmacy">Pharmacy</option>
                      <option value="Administration">Administration</option>
                      <option value="Nursing">Nursing</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone
                    </label>
                    <input
                      type="tel"
                      value={userFormData.phone}
                      onChange={(e) => setUserFormData(prev => ({ ...prev, phone: e.target.value }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Employee ID
                    </label>
                    <input
                      type="text"
                      value={userFormData.employeeId}
                      onChange={(e) => setUserFormData(prev => ({ ...prev, employeeId: e.target.value }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Position
                    </label>
                    <input
                      type="text"
                      value={userFormData.position}
                      onChange={(e) => setUserFormData(prev => ({ ...prev, position: e.target.value }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Specialization
                    </label>
                    <input
                      type="text"
                      value={userFormData.specialization}
                      onChange={(e) => setUserFormData(prev => ({ ...prev, specialization: e.target.value }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      License Number
                    </label>
                    <input
                      type="text"
                      value={userFormData.licenseNumber}
                      onChange={(e) => setUserFormData(prev => ({ ...prev, licenseNumber: e.target.value }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowUserModal(false);
                      setSelectedUser(null);
                      resetUserForm();
                    }}
                    className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={formLoading}
                    className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-blue-300 flex items-center space-x-2"
                  >
                    {formLoading && <Loader className="animate-spin" size={16} />}
                    <span>{selectedUser ? 'Update User' : 'Create User'}</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Role Creation/Edit Modal */}
      {showRoleModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">
                  {selectedRole ? 'Edit Role' : 'Create New Role'}
                </h2>
                <button
                  onClick={() => {
                    setShowRoleModal(false);
                    setSelectedRole(null);
                    resetRoleForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>

              <form onSubmit={selectedRole ? handleUpdateRole : handleCreateRole} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Role Name *
                  </label>
                  <input
                    type="text"
                    value={roleFormData.name}
                    onChange={(e) => setRoleFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                    disabled={selectedRole?.isSystemRole}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
                  />
                  {selectedRole?.isSystemRole && (
                    <p className="text-sm text-gray-500 mt-1">System role names cannot be modified</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    value={roleFormData.description}
                    onChange={(e) => setRoleFormData(prev => ({ ...prev, description: e.target.value }))}
                    required
                    rows={3}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Permissions *
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-4">
                    {availablePermissions.map((permission) => (
                      <label key={permission} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={roleFormData.permissions.includes(permission)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setRoleFormData(prev => ({
                                ...prev,
                                permissions: [...prev.permissions, permission]
                              }));
                            } else {
                              setRoleFormData(prev => ({
                                ...prev,
                                permissions: prev.permissions.filter(p => p !== permission)
                              }));
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">
                          {permission.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      </label>
                    ))}
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    Select at least one permission for this role
                  </p>
                </div>

                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowRoleModal(false);
                      setSelectedRole(null);
                      resetRoleForm();
                    }}
                    className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={formLoading || roleFormData.permissions.length === 0}
                    className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-blue-300 flex items-center space-x-2"
                  >
                    {formLoading && <Loader className="animate-spin" size={16} />}
                    <span>{selectedRole ? 'Update Role' : 'Create Role'}</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}