import React, { useState, useEffect, useRef } from 'react';
import { X, Save, Loader } from 'lucide-react';
import { patientAPI } from '../services/apiService';

interface Patient {
  _id?: string;
  patientId?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  gender: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };
  insurance: {
    provider: string;
    policyNumber: string;
  };
  medicalHistory: string[];
  allergies: string[];
  currentMedications: string[];
}

interface PatientModalProps {
  isOpen: boolean;
  onClose: () => void;
  patient?: Patient | null;
  onSave: () => void;
  readOnly?: boolean;
}

export function PatientModal({ isOpen, onClose, patient, onSave, readOnly = false }: PatientModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Patient>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: 'Male',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: ''
    },
    emergencyContact: {
      name: '',
      relationship: '',
      phone: ''
    },
    insurance: {
      provider: '',
      policyNumber: ''
    },
    medicalHistory: [],
    allergies: [],
    currentMedications: []
  });

  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (patient) {
      setFormData(patient);
    } else {
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        dateOfBirth: '',
        gender: 'Male',
        address: {
          street: '',
          city: '',
          state: '',
          zipCode: ''
        },
        emergencyContact: {
          name: '',
          relationship: '',
          phone: ''
        },
        insurance: {
          provider: '',
          policyNumber: ''
        },
        medicalHistory: [],
        allergies: [],
        currentMedications: []
      });
    }
    setError(null);
  }, [patient, isOpen]);

  // Close on Escape key
  useEffect(() => {
    if (!isOpen) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  // Close on outside click
  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    if (readOnly) return;
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof Patient] as any,
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleArrayChange = (field: 'medicalHistory' | 'allergies' | 'currentMedications', value: string) => {
    if (readOnly) return;
    const items = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({
      ...prev,
      [field]: items
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (patient?._id) {
        await patientAPI.update(patient._id, formData);
      } else {
        await patientAPI.create(formData);
      }
      onSave();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save patient');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  // Helper for initials
  const getInitials = (first: string, last: string) => {
    return `${first?.charAt(0) || ''}${last?.charAt(0) || ''}`.toUpperCase();
  };
  // Helper for age
  const getAge = (dob: string) => {
    if (!dob) return '';
    const birthDate = new Date(dob);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) age--;
    return age;
  };

  return (
    <div
      className="fixed inset-0 flex items-center justify-center p-4 z-50 bg-black/40 dark:bg-black/80 transition-all duration-200 animate-fadeIn"
      onMouseDown={handleOverlayClick}
      style={{ animation: 'fadeIn 0.18s cubic-bezier(.4,0,.2,1)' }}
    >
      <div
        ref={modalRef}
        className="bg-white dark:bg-gray-900 rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-gray-200 dark:border-gray-800 scale-100 transition-transform duration-200 animate-modalPop"
        style={{ animation: 'modalPop 0.18s cubic-bezier(.4,0,.2,1)' }}
      >
        <div className="p-0 md:p-8">
          {/* Header with avatar/initials */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 border-b border-gray-200 dark:border-gray-800 px-6 pt-6 pb-4">
            <div className="flex items-center gap-4">
              <div className="w-14 h-14 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center text-xl font-bold text-gray-700 dark:text-gray-200 border border-gray-200 dark:border-gray-700">
                {getInitials(formData.firstName, formData.lastName)}
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 tracking-tight">
                  {formData.firstName} {formData.lastName}
                </h2>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 font-mono">Patient ID: <span>{formData.patientId || '-'}</span></div>
              </div>
            </div>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-full p-2 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-700"
              aria-label="Close modal"
            >
              <X size={28} />
            </button>
          </div>

          {error && (
            <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <p className="text-red-800 dark:text-red-300">{error}</p>
            </div>
          )}

          <form onSubmit={readOnly ? undefined : handleSubmit} className="space-y-6 px-2 md:px-4 py-6">
            {/* Personal & Demographics */}
            <div>
              <h3 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-2">Personal & Demographics</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Full Name</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.firstName} {formData.lastName}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Gender</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.gender}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Date of Birth</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.dateOfBirth || '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Age</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{getAge(formData.dateOfBirth) || '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Blood Type</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{(formData as any).bloodType || '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Status</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{(formData as any).status || '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Registered</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{(formData as any).createdAt ? new Date((formData as any).createdAt).toLocaleString() : '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Last Updated</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{(formData as any).updatedAt ? new Date((formData as any).updatedAt).toLocaleString() : '-'}</span>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 my-4" />

            {/* Contact & Address */}
            <div>
              <h3 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-2">Contact & Address</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Email</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.email || '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Phone</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.phone || '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Alternate Phone</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{(formData as any).alternatePhone || '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Street</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.address.street || '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">City</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.address.city || '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">State</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.address.state || '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">ZIP Code</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.address.zipCode || '-'}</span>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 my-4" />

            {/* Emergency Contact & Insurance */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
              <div>
                <h3 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-2">Emergency Contact</h3>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500 dark:text-gray-400">Name</div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">{formData.emergencyContact.name || '-'}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Relationship</div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">{formData.emergencyContact.relationship || '-'}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Phone</div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">{formData.emergencyContact.phone || '-'}</div>
                </div>
              </div>
              <div>
                <h3 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-2">Insurance</h3>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500 dark:text-gray-400">Provider</div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">{formData.insurance.provider || '-'}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Policy Number</div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">{formData.insurance.policyNumber || '-'}</div>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 my-4" />

            {/* Assigned Doctor */}
            <div>
              <h3 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-2">Assigned Doctor</h3>
              {((formData as any).assignedDoctor && (formData as any).assignedDoctor.firstName) ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
                  <div>
                    <span className="block text-xs text-gray-500 dark:text-gray-400">Name</span>
                    <span className="block font-medium text-gray-900 dark:text-gray-100">Dr. {(formData as any).assignedDoctor.firstName} {(formData as any).assignedDoctor.lastName}</span>
                  </div>
                  <div>
                    <span className="block text-xs text-gray-500 dark:text-gray-400">Department</span>
                    <span className="block font-medium text-gray-900 dark:text-gray-100">{(formData as any).assignedDoctor.department || '-'}</span>
                  </div>
                </div>
              ) : (
                <div className="text-gray-500 dark:text-gray-400">Not assigned</div>
              )}
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 my-4" />

            {/* Medical Info */}
            <div>
              <h3 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-2">Medical Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Medical History</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.medicalHistory.length ? formData.medicalHistory.join(', ') : '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Allergies</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.allergies.length ? formData.allergies.join(', ') : '-'}</span>
                </div>
                <div>
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Current Medications</span>
                  <span className="block font-medium text-gray-900 dark:text-gray-100">{formData.currentMedications.length ? formData.currentMedications.join(', ') : '-'}</span>
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700 mt-8">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                Close
              </button>
              {!readOnly && (
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {loading ? (
                    <>
                      <Loader className="animate-spin h-4 w-4 mr-2" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      {patient ? 'Update Patient' : 'Add Patient'}
                    </>
                  )}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
