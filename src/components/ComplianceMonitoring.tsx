import React, { useState, useEffect } from 'react';
import {
  Shield,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Clock,
  FileText,
  Lock,
  Eye,
  Download,
  RefreshCw,
  TrendingUp,
  BarChart3,
  Users,
  Database,
  Settings,
  Bell,
  Calendar,
  Activity
} from 'lucide-react';

interface ComplianceStandard {
  id: string;
  name: string;
  description: string;
  status: 'compliant' | 'partial' | 'non_compliant' | 'pending';
  completionPercentage: number;
  lastAudit: string;
  nextAudit: string;
  requirements: ComplianceRequirement[];
}

interface ComplianceRequirement {
  id: string;
  title: string;
  description: string;
  status: 'met' | 'partial' | 'not_met' | 'pending';
  priority: 'high' | 'medium' | 'low';
  dueDate?: string;
  assignedTo?: string;
  evidence?: string[];
}

interface SecurityMetric {
  id: string;
  name: string;
  value: string | number;
  status: 'good' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  description: string;
  lastUpdated: string;
}

export function ComplianceMonitoring() {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Data states
  const [complianceStandards, setComplianceStandards] = useState<ComplianceStandard[]>([]);
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetric[]>([]);
  const [complianceScore, setComplianceScore] = useState(0);

  // Mock data
  const mockStandards: ComplianceStandard[] = [
    {
      id: '1',
      name: 'HIPAA',
      description: 'Health Insurance Portability and Accountability Act',
      status: 'compliant',
      completionPercentage: 95,
      lastAudit: '2024-01-10',
      nextAudit: '2024-07-10',
      requirements: [
        {
          id: '1-1',
          title: 'Administrative Safeguards',
          description: 'Implement administrative safeguards for PHI',
          status: 'met',
          priority: 'high'
        },
        {
          id: '1-2',
          title: 'Physical Safeguards',
          description: 'Implement physical safeguards for PHI',
          status: 'met',
          priority: 'high'
        },
        {
          id: '1-3',
          title: 'Technical Safeguards',
          description: 'Implement technical safeguards for PHI',
          status: 'partial',
          priority: 'high',
          dueDate: '2024-02-01'
        }
      ]
    },
    {
      id: '2',
      name: 'GDPR',
      description: 'General Data Protection Regulation',
      status: 'partial',
      completionPercentage: 78,
      lastAudit: '2024-01-05',
      nextAudit: '2024-04-05',
      requirements: [
        {
          id: '2-1',
          title: 'Data Processing Records',
          description: 'Maintain records of data processing activities',
          status: 'met',
          priority: 'high'
        },
        {
          id: '2-2',
          title: 'Privacy Impact Assessments',
          description: 'Conduct privacy impact assessments',
          status: 'not_met',
          priority: 'high',
          dueDate: '2024-01-25'
        },
        {
          id: '2-3',
          title: 'Data Subject Rights',
          description: 'Implement data subject rights procedures',
          status: 'partial',
          priority: 'medium',
          dueDate: '2024-02-15'
        }
      ]
    },
    {
      id: '3',
      name: 'SOC 2',
      description: 'Service Organization Control 2',
      status: 'pending',
      completionPercentage: 45,
      lastAudit: '2023-12-15',
      nextAudit: '2024-03-15',
      requirements: [
        {
          id: '3-1',
          title: 'Security Controls',
          description: 'Implement comprehensive security controls',
          status: 'partial',
          priority: 'high',
          dueDate: '2024-02-01'
        },
        {
          id: '3-2',
          title: 'Availability Controls',
          description: 'Ensure system availability controls',
          status: 'not_met',
          priority: 'medium',
          dueDate: '2024-02-20'
        }
      ]
    }
  ];

  const mockSecurityMetrics: SecurityMetric[] = [
    {
      id: '1',
      name: 'Failed Login Attempts',
      value: 12,
      status: 'warning',
      trend: 'up',
      description: 'Number of failed login attempts in the last 24 hours',
      lastUpdated: '2024-01-15 10:30:00'
    },
    {
      id: '2',
      name: 'Password Compliance',
      value: '94%',
      status: 'good',
      trend: 'stable',
      description: 'Percentage of users with compliant passwords',
      lastUpdated: '2024-01-15 09:00:00'
    },
    {
      id: '3',
      name: 'Data Encryption',
      value: '100%',
      status: 'good',
      trend: 'stable',
      description: 'Percentage of data encrypted at rest and in transit',
      lastUpdated: '2024-01-15 08:00:00'
    },
    {
      id: '4',
      name: 'Vulnerability Score',
      value: 'Low',
      status: 'good',
      trend: 'down',
      description: 'Overall system vulnerability assessment score',
      lastUpdated: '2024-01-15 07:00:00'
    },
    {
      id: '5',
      name: 'Access Reviews',
      value: '85%',
      status: 'warning',
      trend: 'down',
      description: 'Percentage of access reviews completed on time',
      lastUpdated: '2024-01-15 06:00:00'
    },
    {
      id: '6',
      name: 'Backup Success Rate',
      value: '98%',
      status: 'good',
      trend: 'stable',
      description: 'Percentage of successful automated backups',
      lastUpdated: '2024-01-15 05:00:00'
    }
  ];

  useEffect(() => {
    setComplianceStandards(mockStandards);
    setSecurityMetrics(mockSecurityMetrics);
    
    // Calculate overall compliance score
    const totalScore = mockStandards.reduce((sum, standard) => sum + standard.completionPercentage, 0);
    setComplianceScore(Math.round(totalScore / mockStandards.length));
  }, []);

  const handleRunComplianceCheck = async () => {
    setLoading(true);
    try {
      // Simulate compliance check
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSuccess('Compliance check completed successfully');
    } catch (err) {
      setError('Failed to run compliance check');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = async () => {
    setLoading(true);
    try {
      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 1500));
      setSuccess('Compliance report generated and downloaded');
    } catch (err) {
      setError('Failed to generate report');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant':
      case 'met':
      case 'good':
        return <CheckCircle size={16} className="text-green-500 dark:text-green-400" />;
      case 'partial':
      case 'warning':
        return <AlertTriangle size={16} className="text-yellow-500 dark:text-yellow-400" />;
      case 'non_compliant':
      case 'not_met':
      case 'critical':
        return <XCircle size={16} className="text-red-500 dark:text-red-400" />;
      case 'pending':
        return <Clock size={16} className="text-gray-500 dark:text-gray-400" />;
      default:
        return <Clock size={16} className="text-gray-500 dark:text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'compliant':
      case 'met':
      case 'good':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'partial':
      case 'warning':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'non_compliant':
      case 'not_met':
      case 'critical':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'pending':
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300';
      default:
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={14} className="text-green-500 dark:text-green-400" />;
      case 'down':
        return <TrendingUp size={14} className="text-red-500 dark:text-red-400 rotate-180" />;
      case 'stable':
        return <BarChart3 size={14} className="text-gray-500 dark:text-gray-400" />;
      default:
        return <BarChart3 size={14} className="text-gray-500 dark:text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Compliance & Security Monitoring</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Monitor regulatory compliance and security metrics
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRunComplianceCheck}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
          >
            {loading ? <RefreshCw size={16} className="animate-spin" /> : <Shield size={16} />}
            <span>Run Check</span>
          </button>
          <button
            onClick={handleGenerateReport}
            disabled={loading}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
          >
            <Download size={16} />
            <span>Generate Report</span>
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle size={20} className="text-green-500 dark:text-green-400" />
            <span className="text-green-700 dark:text-green-300">{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle size={20} className="text-red-500 dark:text-red-400" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* Compliance Score Overview */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800/50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Overall Compliance Score</h3>
            <div className="flex items-center space-x-4">
              <div className="text-4xl font-bold text-blue-600 dark:text-blue-400">{complianceScore}%</div>
              <div className="flex items-center space-x-2">
                <TrendingUp size={20} className="text-green-500 dark:text-green-400" />
                <span className="text-sm text-green-600 dark:text-green-400">+2% from last month</span>
              </div>
            </div>
          </div>
          <div className="w-24 h-24 relative">
            <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="currentColor"
                strokeWidth="8"
                fill="transparent"
                className="text-gray-200 dark:text-gray-700"
              />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="currentColor"
                strokeWidth="8"
                fill="transparent"
                strokeDasharray={`${complianceScore * 2.51} 251`}
                className="text-blue-500 dark:text-blue-400"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <Shield size={24} className="text-blue-500 dark:text-blue-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'standards', label: 'Compliance Standards', icon: FileText },
              { id: 'security', label: 'Security Metrics', icon: Shield },
              { id: 'audits', label: 'Audit Trail', icon: Activity }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === id
                    ? 'border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <Icon size={16} />
                <span>{label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Compliance Overview</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {complianceStandards.map(standard => (
                  <div key={standard.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100">{standard.name}</h4>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(standard.status)}
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(standard.status)}`}>
                          {standard.status.replace('_', ' ')}
                        </span>
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{standard.description}</p>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Completion</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {standard.completionPercentage}%
                        </span>
                      </div>
                      <div className="bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-blue-500 dark:bg-blue-400 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${standard.completionPercentage}%` }}
                        />
                      </div>

                      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span>Last audit: {new Date(standard.lastAudit).toLocaleDateString()}</span>
                        <span>Next: {new Date(standard.nextAudit).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'standards' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Compliance Standards Details</h3>

              {complianceStandards.map(standard => (
                <div key={standard.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{standard.name}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{standard.description}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(standard.status)}
                      <span className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(standard.status)}`}>
                        {standard.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h5 className="font-medium text-gray-900 dark:text-gray-100">Requirements</h5>
                    {standard.requirements.map(requirement => (
                      <div key={requirement.id} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              {getStatusIcon(requirement.status)}
                              <h6 className="font-medium text-gray-900 dark:text-gray-100">{requirement.title}</h6>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                requirement.priority === 'high' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' :
                                requirement.priority === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300' :
                                'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                              }`}>
                                {requirement.priority}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{requirement.description}</p>
                            {requirement.dueDate && (
                              <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                                <Calendar size={12} />
                                <span>Due: {new Date(requirement.dueDate).toLocaleDateString()}</span>
                              </div>
                            )}
                          </div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(requirement.status)}`}>
                            {requirement.status.replace('_', ' ')}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Security Metrics</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {securityMetrics.map(metric => (
                  <div key={metric.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">{metric.name}</h4>
                      <div className="flex items-center space-x-1">
                        {getTrendIcon(metric.trend)}
                        {getStatusIcon(metric.status)}
                      </div>
                    </div>

                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                      {metric.value}
                    </div>

                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{metric.description}</p>

                    <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span className={`px-2 py-1 rounded-full ${getStatusColor(metric.status)}`}>
                        {metric.status}
                      </span>
                      <span>Updated: {new Date(metric.lastUpdated).toLocaleString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'audits' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Audit Trail</h3>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
                <div className="text-center py-12">
                  <Activity size={48} className="text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Audit Trail Integration</h4>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Detailed audit trails are available in the Advanced Audit System
                  </p>
                  <button
                    onClick={() => window.location.hash = '#audit'}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 mx-auto transition-colors"
                  >
                    <Eye size={16} />
                    <span>View Audit Logs</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
