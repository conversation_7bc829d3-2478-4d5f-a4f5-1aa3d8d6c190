import React, { useState, useEffect } from 'react';
import { Calendar, Clock, User, Loader } from 'lucide-react';
import { appointmentAPI } from '../services/apiService';

export function AppointmentSchedule() {
  const [appointments, setAppointments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTodaysAppointments();
  }, []);

  const fetchTodaysAppointments = async () => {
    try {
      setLoading(true);
      const today = new Date().toISOString().split('T')[0];
      const response = await appointmentAPI.getAll({
        date: today,
        limit: 5,
        status: 'Scheduled,Confirmed'
      });

      if (response.success) {
        setAppointments(response.data);
        setError(null);
      } else {
        setError('Failed to fetch appointments');
      }
    } catch (err) {
      console.error('Error fetching today\'s appointments:', err);
      setError('Failed to fetch appointments');
      // Set fallback data
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  };
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Confirmed': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'Scheduled': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'In Progress': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'Completed': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'Cancelled': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'No Show': return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Today's Schedule</h2>
        <Calendar size={20} className="text-gray-500 dark:text-gray-400" />
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader className="animate-spin" size={24} />
          <span className="ml-2 text-gray-500 dark:text-gray-400">Loading appointments...</span>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-500 dark:text-red-300 text-sm">{error}</p>
          <button
            onClick={fetchTodaysAppointments}
            className="mt-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
          >
            Try Again
          </button>
        </div>
      ) : appointments.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400 text-sm">No appointments scheduled for today</p>
        </div>
      ) : (
        <div className="space-y-4">
          {appointments.map((appointment) => (
            <div key={appointment._id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Clock size={16} className="text-gray-500 dark:text-gray-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {formatTime(appointment.appointmentTime)}
                  </span>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(appointment.status)}`}>{appointment.status}</span>
              </div>
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <User size={14} className="text-gray-400 dark:text-gray-500" />
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {appointment.patient?.firstName} {appointment.patient?.lastName}
                  </span>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Dr. {appointment.doctor?.firstName} {appointment.doctor?.lastName}
                </div>
                <div className="text-xs text-gray-400 dark:text-gray-500">{appointment.appointmentType}</div>
              </div>
            </div>
          ))}
        </div>
      )}

      <button
        onClick={() => window.location.href = '/appointments'}
        className="w-full mt-4 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
      >
        View All Appointments
      </button>
    </div>
  );
}