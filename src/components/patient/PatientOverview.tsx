import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Edit, 
  Eye, 
  Calendar, 
  UserPlus, 
  AlertCircle, 
  Loader,
  Phone,
  Mail,
  Activity,
  Users,
  TrendingUp,
  Download,
  Trash2
} from 'lucide-react';
import { patientAPI } from '../../services/apiService';
import { appointmentAPI } from '../../services/apiService';
import { PatientModal } from '../PatientModal';

interface Patient {
  _id: string;
  patientId: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  email: string;
  phone: string;
  alternatePhone?: string;
  dateOfBirth: string;
  gender: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
    address?: string;
  };
  insurance: {
    provider: string;
    policyNumber: string;
  };
  bloodType?: string;
  status: string;
  assignedDoctor?: {
    _id: string;
    firstName: string;
    lastName: string;
    department: string;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PatientStats {
  totalPatients: number;
  activePatients: number;
  newPatientsThisMonth: number;
  upcomingAppointments: number;
  criticalAlerts: number;
}

type PatientModalPatient = Patient & {
  medicalHistory: string[];
  allergies: string[];
  currentMedications: string[];
};

export function PatientOverview() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [stats, setStats] = useState<PatientStats>({
    totalPatients: 0,
    activePatients: 0,
    newPatientsThisMonth: 0,
    upcomingAppointments: 0,
    criticalAlerts: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [filters, setFilters] = useState({
    ageRange: { min: '', max: '' },
    gender: '',
    bloodType: '',
    status: '',
    assignedDoctor: '',
    department: '',
    insuranceProvider: '',
    hasChronicConditions: '',
    hasAllergies: '',
    registrationDate: { start: '', end: '' },
    lastVisitDate: { start: '', end: '' }
  });
  const [allPatients, setAllPatients] = useState<Patient[]>([]);
  const [showPatientModal, setShowPatientModal] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<PatientModalPatient | null>(null);
  const [modalReadOnly, setModalReadOnly] = useState(true);

  useEffect(() => {
    fetchAllPatients();
  }, []);

  useEffect(() => {
    applyClientSideFilters();
  }, [allPatients, filters, searchTerm]);

  const fetchAllPatients = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await patientAPI.getAll({ limit: 10000 });
      if (response.success) {
        const patients = response.data || [];
        setAllPatients(patients);
        await computeAndSetStats(patients);
      } else {
        throw new Error(response.error || 'Failed to fetch patients');
      }
    } catch (err: unknown) {
      setError((err as Error).message || 'Failed to fetch patients');
      setAllPatients([]);
      setStats({
        totalPatients: 0,
        activePatients: 0,
        newPatientsThisMonth: 0,
        upcomingAppointments: 0,
        criticalAlerts: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const computeAndSetStats = async (patients: Patient[]) => {
    // Total patients
    const totalPatients = patients.length;
    // Active patients
    const activePatients = patients.filter(p => p.status === 'Active' || p.isActive).length;
    // New this month
    const now = new Date();
    const thisMonth = now.getMonth();
    const thisYear = now.getFullYear();
    const newPatientsThisMonth = patients.filter(p => {
      const created = new Date(p.createdAt);
      return created.getMonth() === thisMonth && created.getFullYear() === thisYear;
    }).length;
    // Critical alerts (patients with allergies)
    const criticalAlerts = patients.filter((p: Patient & { allergies?: string[]; medicalHistory?: { allergies?: string[] } }) => {
      if (Array.isArray(p.allergies) && p.allergies.length > 0) return true;
      if (p.medicalHistory?.allergies && Array.isArray(p.medicalHistory.allergies) && p.medicalHistory.allergies.length > 0) return true;
      return false;
    }).length;
    // Upcoming appointments
    let upcomingAppointments = 0;
    try {
      const appointmentsRes = await appointmentAPI.getAll({ limit: 10000 });
      if (appointmentsRes.success) {
        const now = new Date();
        upcomingAppointments = (appointmentsRes.data || []).filter((appt: { appointmentDate: string; status: string }) => {
          const apptDate = new Date(appt.appointmentDate);
          return (
            apptDate >= now &&
            appt.status !== 'Cancelled' &&
            appt.status !== 'Completed'
          );
        }).length;
      }
    } catch (err) {
      console.error('Error fetching appointments for stats:', err);
    }
    setStats({
      totalPatients,
      activePatients,
      newPatientsThisMonth,
      upcomingAppointments,
      criticalAlerts
    });
  };

  const applyClientSideFilters = () => {
    let filtered = [...allPatients];
    // Search
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        p.firstName.toLowerCase().includes(search) ||
        p.lastName.toLowerCase().includes(search) ||
        (p.patientId && p.patientId.toLowerCase().includes(search)) ||
        (p.email && p.email.toLowerCase().includes(search)) ||
        (p.phone && p.phone.toLowerCase().includes(search))
      );
    }
    // Filters
    if (filters.ageRange.min) {
      filtered = filtered.filter(p => calculateAge(p.dateOfBirth) >= Number(filters.ageRange.min));
    }
    if (filters.ageRange.max) {
      filtered = filtered.filter(p => calculateAge(p.dateOfBirth) <= Number(filters.ageRange.max));
    }
    if (filters.gender) {
      filtered = filtered.filter(p => p.gender === filters.gender);
    }
    if (filters.bloodType) {
      filtered = filtered.filter(p => p.bloodType === filters.bloodType);
    }
    if (filters.status) {
      filtered = filtered.filter(p => p.status === filters.status);
    }
    if (filters.department) {
      filtered = filtered.filter(p => p.assignedDoctor && p.assignedDoctor.department === filters.department);
    }
    if (filters.insuranceProvider) {
      filtered = filtered.filter(p => p.insurance && p.insurance.provider === filters.insuranceProvider);
    }
    if (filters.hasChronicConditions) {
      filtered = filtered.filter((p: Patient & { hasChronicConditions?: boolean }) =>
        filters.hasChronicConditions === 'yes' ? p.hasChronicConditions : !p.hasChronicConditions
      );
    }
    if (filters.hasAllergies) {
      filtered = filtered.filter((p: Patient & { hasAllergies?: boolean }) =>
        filters.hasAllergies === 'yes' ? p.hasAllergies : !p.hasAllergies
      );
    }
    if (filters.registrationDate.start) {
      filtered = filtered.filter(p => new Date(p.createdAt) >= new Date(filters.registrationDate.start));
    }
    if (filters.registrationDate.end) {
      filtered = filtered.filter(p => new Date(p.createdAt) <= new Date(filters.registrationDate.end));
    }
    if (filters.lastVisitDate.start) {
      filtered = filtered.filter((p: Patient & { lastVisitDate?: string }) =>
        p.lastVisitDate && new Date(p.lastVisitDate) >= new Date(filters.lastVisitDate.start)
      );
    }
    if (filters.lastVisitDate.end) {
      filtered = filtered.filter((p: Patient & { lastVisitDate?: string }) =>
        p.lastVisitDate && new Date(p.lastVisitDate) <= new Date(filters.lastVisitDate.end)
      );
    }
    setPatients(filtered);
  };

  // fetchStats removed; stats are now computed after fetching patients

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  const handleViewPatient = (patientId: string) => {
    const patient = allPatients.find(p => p._id === patientId) as (Patient & { medicalHistory?: { medicalHistory?: string[]; allergies?: string[]; medications?: string[] } }) | undefined;
    if (patient) {
      const insurance = patient.insurance && patient.insurance.provider && patient.insurance.policyNumber
        ? { provider: patient.insurance.provider, policyNumber: patient.insurance.policyNumber }
        : { provider: '', policyNumber: '' };
      const modalPatient: PatientModalPatient = Object.assign({}, patient, {
        email: patient.email || '',
        insurance,
        medicalHistory: patient.medicalHistory?.medicalHistory || [],
        allergies: patient.medicalHistory?.allergies || [],
        currentMedications: patient.medicalHistory?.medications || [],
      });
      setSelectedPatient(modalPatient);
    } else {
      setSelectedPatient(null);
    }
    setModalReadOnly(true);
    setShowPatientModal(true);
  };

  const handleEditPatient = (patientId: string) => {
    const patient = allPatients.find(p => p._id === patientId) as (Patient & { medicalHistory?: { medicalHistory?: string[]; allergies?: string[]; medications?: string[] } }) | undefined;
    if (patient) {
      const insurance = patient.insurance && patient.insurance.provider && patient.insurance.policyNumber
        ? { provider: patient.insurance.provider, policyNumber: patient.insurance.policyNumber }
        : { provider: '', policyNumber: '' };
      const modalPatient: PatientModalPatient = Object.assign({}, patient, {
        email: patient.email || '',
        insurance,
        medicalHistory: patient.medicalHistory?.medicalHistory || [],
        allergies: patient.medicalHistory?.allergies || [],
        currentMedications: patient.medicalHistory?.medications || [],
      });
      setSelectedPatient(modalPatient);
      setModalReadOnly(false);
      setShowPatientModal(true);
    }
  };

  const handleDeletePatient = async (patientId: string) => {
    if (window.confirm('Are you sure you want to delete this patient?')) {
      try {
        const response = await patientAPI.delete(patientId);
        if (response.success) {
          alert('Patient deleted successfully');
          fetchAllPatients(); // Refresh the list
          // fetchStats(); // Refresh stats - now handled by computeAndSetStats
        } else {
          alert('Failed to delete patient');
        }
      } catch (err) {
        console.error('Error deleting patient:', err);
        alert('Error deleting patient');
      }
    }
  };

  const handleAddPatient = () => {
    window.location.href = '/patients/registration';
  };

  const handleApplyFilters = () => {
    // No API call needed, filtering is now client-side
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Patient Overview</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">Comprehensive patient management dashboard</p>
        </div>
        <button
          onClick={handleAddPatient}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <UserPlus size={20} />
          <span>Add New Patient</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Patients</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.totalPatients.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600 dark:text-blue-300" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Patients</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-300">{stats.activePatients.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-green-600 dark:text-green-300" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">New This Month</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-300">{stats.newPatientsThisMonth}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-blue-600 dark:text-blue-300" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Upcoming Appointments</p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-300">{stats.upcomingAppointments}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-purple-600 dark:text-purple-300" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Critical Alerts</p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-300">{stats.criticalAlerts}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-red-600 dark:text-red-300" />
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Search and Filters */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <form className="space-y-4">
          {/* Basic Search */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={20} />
              <input
                type="text"
                placeholder="Search patients by name, ID, phone, email, or medical conditions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              />
            </div>
            {/* Search button removed for instant search */}
            <button
              type="button"
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className={`px-4 py-2 rounded-lg transition-colors flex items-center space-x-2 ${
                showAdvancedFilters
                  ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-300 dark:border-blue-700'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <Filter size={20} />
              <span>Advanced Filters</span>
            </button>
            <button
              type="button"
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
            >
              <Download size={20} />
              <span>Export</span>
            </button>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="border-t pt-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Age Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Age Range</label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      placeholder="Min"
                      value={filters.ageRange.min}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        ageRange: { ...prev.ageRange, min: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    />
                    <input
                      type="number"
                      placeholder="Max"
                      value={filters.ageRange.max}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        ageRange: { ...prev.ageRange, max: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    />
                  </div>
                </div>

                {/* Gender */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Gender</label>
                  <select
                    value={filters.gender}
                    onChange={(e) => setFilters(prev => ({ ...prev, gender: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  >
                    <option value="">All Genders</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                {/* Blood Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Blood Type</label>
                  <select
                    value={filters.bloodType}
                    onChange={(e) => setFilters(prev => ({ ...prev, bloodType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  >
                    <option value="">All Blood Types</option>
                    <option value="A+">A+</option>
                    <option value="A-">A-</option>
                    <option value="B+">B+</option>
                    <option value="B-">B-</option>
                    <option value="AB+">AB+</option>
                    <option value="AB-">AB-</option>
                    <option value="O+">O+</option>
                    <option value="O-">O-</option>
                  </select>
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  >
                    <option value="">All Status</option>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                    <option value="Discharged">Discharged</option>
                  </select>
                </div>

                {/* Department */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department</label>
                  <select
                    value={filters.department}
                    onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  >
                    <option value="">All Departments</option>
                    <option value="Cardiology">Cardiology</option>
                    <option value="Neurology">Neurology</option>
                    <option value="Orthopedics">Orthopedics</option>
                    <option value="Pediatrics">Pediatrics</option>
                    <option value="General Medicine">General Medicine</option>
                  </select>
                </div>

                {/* Insurance Provider */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Insurance</label>
                  <select
                    value={filters.insuranceProvider}
                    onChange={(e) => setFilters(prev => ({ ...prev, insuranceProvider: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  >
                    <option value="">All Insurance</option>
                    <option value="Blue Cross Blue Shield">Blue Cross Blue Shield</option>
                    <option value="Aetna">Aetna</option>
                    <option value="Medicare">Medicare</option>
                    <option value="Medicaid">Medicaid</option>
                    <option value="Self-Pay">Self-Pay</option>
                  </select>
                </div>

                {/* Chronic Conditions */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Chronic Conditions</label>
                  <select
                    value={filters.hasChronicConditions}
                    onChange={(e) => setFilters(prev => ({ ...prev, hasChronicConditions: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  >
                    <option value="">All Patients</option>
                    <option value="yes">Has Chronic Conditions</option>
                    <option value="no">No Chronic Conditions</option>
                  </select>
                </div>

                {/* Allergies */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Allergies</label>
                  <select
                    value={filters.hasAllergies}
                    onChange={(e) => setFilters(prev => ({ ...prev, hasAllergies: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  >
                    <option value="">All Patients</option>
                    <option value="yes">Has Allergies</option>
                    <option value="no">No Known Allergies</option>
                  </select>
                </div>
              </div>

              {/* Date Ranges */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Registration Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Registration Date</label>
                  <div className="flex space-x-2">
                    <input
                      type="date"
                      value={filters.registrationDate.start}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        registrationDate: { ...prev.registrationDate, start: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    />
                    <input
                      type="date"
                      value={filters.registrationDate.end}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        registrationDate: { ...prev.registrationDate, end: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    />
                  </div>
                </div>

                {/* Last Visit Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Last Visit Date</label>
                  <div className="flex space-x-2">
                    <input
                      type="date"
                      value={filters.lastVisitDate.start}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        lastVisitDate: { ...prev.lastVisitDate, start: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    />
                    <input
                      type="date"
                      value={filters.lastVisitDate.end}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        lastVisitDate: { ...prev.lastVisitDate, end: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    />
                  </div>
                </div>
              </div>

              {/* Filter Actions */}
              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {Object.values(filters).some(value =>
                    typeof value === 'string' ? value !== '' :
                    typeof value === 'object' ? Object.values(value).some(v => v !== '') : false
                  ) && (
                    <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs">Filters Applied</span>
                  )}
                </div>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => setFilters({
                      ageRange: { min: '', max: '' },
                      gender: '',
                      bloodType: '',
                      status: '',
                      assignedDoctor: '',
                      department: '',
                      insuranceProvider: '',
                      hasChronicConditions: '',
                      hasAllergies: '',
                      registrationDate: { start: '', end: '' },
                      lastVisitDate: { start: '', end: '' }
                    })}
                    className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 transition-colors"
                  >
                    Clear All
                  </button>
                  <button
                    type="button"
                    onClick={handleApplyFilters}
                    className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
                  >
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          )}
        </form>
      </div>

      {/* Recent Patients */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Recent Patients</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">Latest patient registrations and updates</p>
        </div>

        {error && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 dark:border-red-800">
            <AlertCircle className="h-5 w-5 text-red-400 dark:text-red-300" />
              <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Patient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Age/Gender
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Assigned Doctor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {patients.map((patient: Patient) => (
                <tr key={patient._id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 dark:text-blue-300 font-medium">
                          {patient.firstName.charAt(0)}{patient.lastName.charAt(0)}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {patient.firstName} {patient.lastName}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">ID: {patient.patientId}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100 flex items-center">
                      <Phone size={14} className="mr-1 text-gray-400 dark:text-gray-500" />
                      {patient.phone}
                    </div>
                    {patient.email && (
                      <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                        <Mail size={14} className="mr-1 text-gray-400 dark:text-gray-500" />
                        {patient.email}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      {calculateAge(patient.dateOfBirth)} years
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">{patient.gender}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {patient.assignedDoctor ? (
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Dr. {patient.assignedDoctor.firstName} {patient.assignedDoctor.lastName}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">{patient.assignedDoctor.department}</div>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-400 dark:text-gray-500">Not assigned</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      patient.status === 'Active' 
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                        : patient.status === 'Inactive'
                        ? 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                        : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                    }`}>
                      {patient.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewPatient(patient._id)}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 p-1 rounded"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => handleEditPatient(patient._id)}
                        className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded"
                        title="Edit Patient"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => window.location.href = '/appointments'}
                        className="text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300 p-1 rounded"
                        title="Schedule Appointment"
                      >
                        <Calendar size={16} />
                      </button>
                      <button
                        onClick={() => handleDeletePatient(patient._id)}
                        className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded"
                        title="Delete Patient"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {patients.length === 0 && !loading && (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No patients found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Get started by adding a new patient to the system.
            </p>
          </div>
        )}
      </div>
      <PatientModal
        isOpen={showPatientModal}
        onClose={() => setShowPatientModal(false)}
        patient={selectedPatient}
        onSave={async () => {
          await fetchAllPatients();
          setShowPatientModal(false);
        }}
        readOnly={modalReadOnly}
      />
    </div>
  );
}
