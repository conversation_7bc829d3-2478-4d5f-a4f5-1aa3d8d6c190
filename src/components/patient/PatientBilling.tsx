import React, { useState, useEffect } from 'react';
import { 
  DollarSign, 
  CreditCard, 
  FileText, 
  Calendar, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Download,
  Eye,
  Plus,
  Search,
  Filter,
  Loader,
  TrendingUp,
  TrendingDown,
  Receipt,
  Shield
} from 'lucide-react';

interface PatientBill {
  _id: string;
  billId: string;
  patient: {
    _id: string;
    firstName: string;
    lastName: string;
    patientId: string;
  };
  visit?: {
    _id: string;
    visitId: string;
    visitDate: string;
  };
  billDate: string;
  dueDate: string;
  totalAmount: number;
  paidAmount: number;
  outstandingAmount: number;
  status: string;
  paymentMethod?: string;
  insuranceClaim?: {
    claimId: string;
    provider: string;
    status: string;
    claimedAmount: number;
    approvedAmount: number;
  };
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    category: string;
  }>;
  createdAt: string;
}

interface FinancialStats {
  totalRevenue: number;
  outstandingAmount: number;
  paidToday: number;
  insurancePending: number;
  averageBillAmount: number;
  collectionRate: number;
}

export function PatientBilling() {
  const [bills, setBills] = useState<PatientBill[]>([]);
  const [stats, setStats] = useState<FinancialStats>({
    totalRevenue: 0,
    outstandingAmount: 0,
    paidToday: 0,
    insurancePending: 0,
    averageBillAmount: 0,
    collectionRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedBill, setSelectedBill] = useState<PatientBill | null>(null);
  const [showNewBillModal, setShowNewBillModal] = useState(false);

  useEffect(() => {
    fetchBills();
    fetchStats();
  }, [searchTerm, filterStatus]);

  const fetchBills = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockBills: PatientBill[] = [
        {
          _id: '1',
          billId: 'BILL001234',
          patient: {
            _id: 'p1',
            firstName: 'John',
            lastName: 'Doe',
            patientId: 'PT001234'
          },
          visit: {
            _id: 'v1',
            visitId: 'VIS001234',
            visitDate: new Date().toISOString()
          },
          billDate: new Date().toISOString(),
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          totalAmount: 1250.00,
          paidAmount: 750.00,
          outstandingAmount: 500.00,
          status: 'Partially Paid',
          paymentMethod: 'Insurance + Cash',
          insuranceClaim: {
            claimId: 'CLM001234',
            provider: 'Blue Cross Blue Shield',
            status: 'Approved',
            claimedAmount: 1000.00,
            approvedAmount: 750.00
          },
          items: [
            {
              description: 'Consultation Fee',
              quantity: 1,
              unitPrice: 200.00,
              totalPrice: 200.00,
              category: 'Professional Services'
            },
            {
              description: 'ECG Test',
              quantity: 1,
              unitPrice: 150.00,
              totalPrice: 150.00,
              category: 'Diagnostic Tests'
            },
            {
              description: 'Blood Work Panel',
              quantity: 1,
              unitPrice: 300.00,
              totalPrice: 300.00,
              category: 'Laboratory'
            },
            {
              description: 'Medications',
              quantity: 1,
              unitPrice: 600.00,
              totalPrice: 600.00,
              category: 'Pharmacy'
            }
          ],
          createdAt: new Date().toISOString()
        }
      ];
      setBills(mockBills);
    } catch (err) {
      setError('Failed to fetch bills');
      console.error('Error fetching bills:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Mock stats - replace with actual API call
      setStats({
        totalRevenue: 125000.00,
        outstandingAmount: 25000.00,
        paidToday: 5500.00,
        insurancePending: 15000.00,
        averageBillAmount: 850.00,
        collectionRate: 85.5
      });
    } catch (err) {
      console.error('Error fetching stats:', err);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Partially Paid':
        return 'bg-yellow-100 text-yellow-800';
      case 'Pending':
        return 'bg-blue-100 text-blue-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      case 'Cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Paid':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'Partially Paid':
        return <Clock size={16} className="text-yellow-600" />;
      case 'Pending':
        return <Clock size={16} className="text-blue-600" />;
      case 'Overdue':
        return <AlertCircle size={16} className="text-red-600" />;
      default:
        return <FileText size={16} className="text-gray-600" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Patient Billing</h1>
          <p className="text-gray-600 mt-1">Financial overview and billing management</p>
        </div>
        <button
          onClick={() => setShowNewBillModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus size={20} />
          <span>New Bill</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatCurrency(stats.totalRevenue)}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-300" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Outstanding</p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400">{formatCurrency(stats.outstandingAmount)}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
              <TrendingDown className="w-6 h-6 text-red-600 dark:text-red-300" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Paid Today</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{formatCurrency(stats.paidToday)}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-blue-600 dark:text-blue-300" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Insurance Pending</p>
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{formatCurrency(stats.insurancePending)}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6 text-orange-600 dark:text-orange-300" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Bill Amount</p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{formatCurrency(stats.averageBillAmount)}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
              <Receipt className="w-6 h-6 text-purple-600 dark:text-purple-300" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Collection Rate</p>
              <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{stats.collectionRate}%</p>
            </div>
            <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-indigo-600 dark:text-indigo-300" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search bills by patient name, bill ID, or amount..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="Paid">Paid</option>
              <option value="Partially Paid">Partially Paid</option>
              <option value="Pending">Pending</option>
              <option value="Overdue">Overdue</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bills Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Billing Records</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">All patient bills and payments</p>
        </div>
        {error && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 dark:border-red-800">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400 dark:text-red-300" />
              <div className="ml-3">
                <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
              </div>
            </div>
          </div>
        )}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bill Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Patient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Insurance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {bills.map((bill) => (
                <tr key={bill._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Receipt size={16} className="text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {bill.billId}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(bill.billDate).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-gray-400">
                          Due: {new Date(bill.dueDate).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {bill.patient.firstName} {bill.patient.lastName}
                    </div>
                    <div className="text-sm text-gray-500">ID: {bill.patient.patientId}</div>
                    {bill.visit && (
                      <div className="text-xs text-gray-400">Visit: {bill.visit.visitId}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm space-y-1">
                      <div className="font-medium text-gray-900">
                        Total: {formatCurrency(bill.totalAmount)}
                      </div>
                      <div className="text-green-600">
                        Paid: {formatCurrency(bill.paidAmount)}
                      </div>
                      <div className="text-red-600">
                        Outstanding: {formatCurrency(bill.outstandingAmount)}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {bill.insuranceClaim ? (
                      <div className="text-xs space-y-1">
                        <div className="font-medium text-gray-900">
                          {bill.insuranceClaim.provider}
                        </div>
                        <div className="text-gray-500">
                          Claim: {bill.insuranceClaim.claimId}
                        </div>
                        <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          bill.insuranceClaim.status === 'Approved' ? 'bg-green-100 text-green-800' :
                          bill.insuranceClaim.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {bill.insuranceClaim.status}
                        </div>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-400">No insurance</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(bill.status)}`}>
                      {getStatusIcon(bill.status)}
                      <span className="ml-1">{bill.status}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setSelectedBill(bill)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => {/* Handle download */}}
                        className="text-green-600 hover:text-green-900 p-1 rounded"
                        title="Download PDF"
                      >
                        <Download size={16} />
                      </button>
                      <button
                        onClick={() => {/* Handle payment */}}
                        className="text-purple-600 hover:text-purple-900 p-1 rounded"
                        title="Record Payment"
                      >
                        <CreditCard size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {bills.length === 0 && !loading && (
          <div className="text-center py-12">
            <Receipt className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No bills found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start by creating a new bill for a patient visit.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
