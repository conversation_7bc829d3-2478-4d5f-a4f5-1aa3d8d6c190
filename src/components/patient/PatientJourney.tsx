import React, { useState, useEffect } from 'react';
import { patientAPI } from '../../services/apiService';
import {
  UserPlus,
  Calendar,
  Clock,
  Stethoscope,
  FileText,
  Pill,
  CreditCard,
  Home,
  CheckCircle,
  AlertCircle,
  User,
  Activity,
  Eye,
  Edit,
  Plus,
  RefreshCw
} from 'lucide-react';

interface PatientJourneyStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'skipped';
  icon: React.ComponentType<any>;
  estimatedTime: string;
  department?: string;
  assignedStaff?: string;
  completedAt?: string;
  notes?: string;
}

interface PatientJourneyData {
  patientId: string;
  patientName: string;
  currentStep: number;
  totalSteps: number;
  journeyStarted: string;
  estimatedCompletion: string;
  steps: PatientJourneyStep[];
  currentLocation: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export function PatientJourney() {
  const [selectedPatient, setSelectedPatient] = useState<string>('');
  const [journeyData, setJourneyData] = useState<PatientJourneyData | null>(null);
  const [loading, setLoading] = useState(false);
  const [showStepDetails, setShowStepDetails] = useState<string | null>(null);
  const [patients, setPatients] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Fetch patients on component mount
  useEffect(() => {
    fetchPatients();
  }, []);

  const fetchPatients = async () => {
    try {
      setLoading(true);
      const response = await patientAPI.getAll({ limit: 50 });
      setPatients(response.data || []);
    } catch (error) {
      console.error('Error fetching patients:', error);
      setError('Failed to load patients');
    } finally {
      setLoading(false);
    }
  };

  const generateJourneyData = (patient: any): PatientJourneyData => {
    const now = new Date();
    const journeyStarted = new Date(now.getTime() - Math.random() * 4 * 60 * 60 * 1000);
    const estimatedCompletion = new Date(now.getTime() + Math.random() * 6 * 60 * 60 * 1000);

    const allSteps = [
      {
        id: 'registration',
        title: 'Patient Registration',
        description: 'Complete patient registration and verify insurance',
        status: 'completed' as const,
        icon: UserPlus,
        estimatedTime: '10 min',
        department: 'Front Desk',
        assignedStaff: 'Lisa Anderson',
        completedAt: new Date(journeyStarted.getTime() + 10 * 60 * 1000).toISOString(),
        notes: 'Registration completed successfully. Insurance verified.'
      },
      {
        id: 'appointment',
        title: 'Appointment Check-in',
        description: 'Check-in for scheduled appointment',
        status: 'completed' as const,
        icon: Calendar,
        estimatedTime: '5 min',
        department: 'Front Desk',
        assignedStaff: 'Reception Staff',
        completedAt: new Date(journeyStarted.getTime() + 20 * 60 * 1000).toISOString()
      },
      {
        id: 'vitals',
        title: 'Vital Signs',
        description: 'Record vital signs and basic measurements',
        status: Math.random() > 0.3 ? 'completed' as const : 'in-progress' as const,
        icon: Activity,
        estimatedTime: '15 min',
        department: 'Nursing',
        assignedStaff: 'Mary Wilson',
        completedAt: Math.random() > 0.3 ? new Date(journeyStarted.getTime() + 35 * 60 * 1000).toISOString() : undefined
      },
      {
        id: 'consultation',
        title: 'Doctor Consultation',
        description: 'Medical examination and consultation',
        status: Math.random() > 0.5 ? 'in-progress' as const : 'pending' as const,
        icon: Stethoscope,
        estimatedTime: '30 min',
        department: 'General Medicine',
        assignedStaff: patient.assignedDoctor?.firstName ? `${patient.assignedDoctor.firstName} ${patient.assignedDoctor.lastName}` : 'Dr. Sarah Johnson'
      },
      {
        id: 'diagnosis',
        title: 'Diagnosis & Treatment Plan',
        description: 'Finalize diagnosis and create treatment plan',
        status: 'pending' as const,
        icon: FileText,
        estimatedTime: '20 min',
        department: 'General Medicine'
      },
      {
        id: 'pharmacy',
        title: 'Pharmacy',
        description: 'Prescription fulfillment and medication counseling',
        status: 'pending' as const,
        icon: Pill,
        estimatedTime: '15 min',
        department: 'Pharmacy'
      },
      {
        id: 'billing',
        title: 'Billing & Payment',
        description: 'Process billing and payment',
        status: 'pending' as const,
        icon: CreditCard,
        estimatedTime: '10 min',
        department: 'Billing'
      },
      {
        id: 'discharge',
        title: 'Discharge',
        description: 'Complete discharge process and follow-up instructions',
        status: 'pending' as const,
        icon: Home,
        estimatedTime: '10 min',
        department: 'Front Desk'
      }
    ];

    const currentStep = allSteps.findIndex(step => step.status === 'in-progress') + 1 ||
                       allSteps.filter(step => step.status === 'completed').length + 1;

    return {
      patientId: patient.patientId || `PT${Math.random().toString().substring(2, 8)}`,
      patientName: `${patient.firstName} ${patient.lastName}`,
      currentStep,
      totalSteps: allSteps.length,
      journeyStarted: journeyStarted.toISOString(),
      estimatedCompletion: estimatedCompletion.toISOString(),
      currentLocation: getCurrentLocation(currentStep),
      priority: getPriority(patient),
      steps: allSteps
    };
  };

  const getCurrentLocation = (currentStep: number): string => {
    const locations = [
      'Front Desk', 'Waiting Area', 'Triage Room', 'Consultation Room 2',
      'Consultation Room 2', 'Pharmacy', 'Billing Office', 'Exit'
    ];
    return locations[Math.min(currentStep - 1, locations.length - 1)] || 'Front Desk';
  };

  const getPriority = (patient: any): 'low' | 'medium' | 'high' | 'urgent' => {
    const age = patient.dateOfBirth ?
      Math.floor((new Date().getTime() - new Date(patient.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : 30;

    if (age > 70 || patient.medicalHistory?.chronicConditions?.length > 2) return 'high';
    if (age > 60 || patient.medicalHistory?.allergies?.length > 0) return 'medium';
    return 'low';
  };

  const fetchPatientJourney = async () => {
    if (!selectedPatient) return;

    try {
      setLoading(true);
      setError(null);

      // Find the selected patient
      const patient = patients.find(p => p._id === selectedPatient);
      if (patient) {
        // Generate journey data based on patient information
        const journeyData = generateJourneyData(patient);
        setJourneyData(journeyData);
      } else {
        setError('Patient not found');
      }
    } catch (error) {
      console.error('Error fetching patient journey:', error);
      setError('Failed to load patient journey');
    } finally {
      setLoading(false);
    }
  };

  const handlePatientSelect = (patientId: string) => {
    setSelectedPatient(patientId);
    const patient = patients.find(p => p._id === patientId);
    if (patient) {
      setJourneyData(generateJourneyData(patient));
    }
  };



  useEffect(() => {
    if (selectedPatient) {
      fetchPatientJourney();
    }
  }, [selectedPatient]);



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending':
        return 'bg-gray-100 text-gray-600 border-gray-200';
      case 'skipped':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'high':
        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300';
      case 'medium':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'low':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={20} className="text-green-600 dark:text-green-400" />;
      case 'in-progress':
        return <Clock size={20} className="text-blue-600 dark:text-blue-400" />;
      case 'pending':
        return <Clock size={20} className="text-gray-400 dark:text-gray-500" />;
      case 'skipped':
        return <AlertCircle size={20} className="text-yellow-600 dark:text-yellow-400" />;
      default:
        return <Clock size={20} className="text-gray-400 dark:text-gray-500" />;
    }
  };

  const updateStepStatus = async (stepId: string, newStatus: string, notes?: string) => {
    if (!journeyData) return;

    const updatedSteps = journeyData.steps.map(step => {
      if (step.id === stepId) {
        return {
          ...step,
          status: newStatus as any,
          completedAt: newStatus === 'completed' ? new Date().toISOString() : step.completedAt,
          notes: notes || step.notes
        };
      }
      return step;
    });

    // Update current step
    const currentStepIndex = updatedSteps.findIndex(step => step.status === 'in-progress');
    const newCurrentStep = currentStepIndex >= 0 ? currentStepIndex + 1 : journeyData.currentStep;

    setJourneyData({
      ...journeyData,
      steps: updatedSteps,
      currentStep: newCurrentStep
    });

    // Here you would make an API call to update the backend
    console.log('Updating step:', stepId, 'to status:', newStatus);
  };

  const calculateProgress = () => {
    if (!journeyData) return 0;
    const completedSteps = journeyData.steps.filter(step => step.status === 'completed').length;
    return (completedSteps / journeyData.totalSteps) * 100;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Patient Journey</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">Track complete patient flow from registration to discharge</p>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center space-x-2">
          <Plus size={20} />
          <span>New Patient Journey</span>
        </button>
      </div>

      {/* Patient Selection */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Select Patient</h3>
        {error && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-800 dark:text-red-300 text-sm">{error}</p>
          </div>
        )}
        <div className="flex space-x-4">
          <select
            value={selectedPatient}
            onChange={(e) => handlePatientSelect(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            disabled={loading}
          >
            <option value="">
              {loading ? 'Loading patients...' : 'Select a patient to track their journey...'}
            </option>
            {patients.map((patient) => (
              <option key={patient._id} value={patient._id}>
                {patient.firstName} {patient.lastName} - {patient.patientId}
              </option>
            ))}
          </select>
          <button
            onClick={() => fetchPatients()}
            disabled={loading}
            className="bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-500 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            title="Refresh patient list"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <RefreshCw size={16} />
            )}
          </button>
        </div>
      </div>

      {/* Journey Overview */}
      {journeyData && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{journeyData.patientName}</h3>
              <p className="text-gray-600 dark:text-gray-400">Patient ID: {journeyData.patientId}</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(journeyData.priority)}`}>{journeyData.priority.charAt(0).toUpperCase() + journeyData.priority.slice(1)} Priority</span>
              <div className="text-right">
                <p className="text-sm text-gray-600 dark:text-gray-400">Current Location</p>
                <p className="font-medium text-gray-900 dark:text-gray-100">{journeyData.currentLocation}</p>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Journey Progress</span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Step {journeyData.currentStep} of {journeyData.totalSteps}
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div
                className="bg-blue-600 dark:bg-blue-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${calculateProgress()}%` }}
              ></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Started: {new Date(journeyData.journeyStarted).toLocaleTimeString()}</span>
              <span>Est. Completion: {new Date(journeyData.estimatedCompletion).toLocaleTimeString()}</span>
            </div>
          </div>

          {/* Journey Steps */}
          <div className="space-y-4">
            {journeyData.steps.map((step, index) => {
              const IconComponent = step.icon;
              const isActive = step.status === 'in-progress';
              const isCompleted = step.status === 'completed';
              
              return (
                <div
                  key={step.id}
                  className={`relative p-4 rounded-lg border-2 transition-all ${
                    isActive ? 'border-blue-300 bg-blue-50' : 
                    isCompleted ? 'border-green-300 bg-green-50' : 
                    'border-gray-200 bg-white'
                  }`}
                >
                  {/* Connection Line */}
                  {index < journeyData.steps.length - 1 && (
                    <div className="absolute left-8 top-16 w-0.5 h-8 bg-gray-300"></div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    {/* Step Icon */}
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                      isCompleted ? 'bg-green-100' : 
                      isActive ? 'bg-blue-100' : 
                      'bg-gray-100'
                    }`}>
                      <IconComponent size={20} className={
                        isCompleted ? 'text-green-600' : 
                        isActive ? 'text-blue-600' : 
                        'text-gray-400'
                      } />
                    </div>

                    {/* Step Content */}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900">{step.title}</h4>
                          <p className="text-sm text-gray-600">{step.description}</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <span className="text-xs text-gray-500">
                              <Clock size={12} className="inline mr-1" />
                              {step.estimatedTime}
                            </span>
                            {step.department && (
                              <span className="text-xs text-gray-500">
                                <User size={12} className="inline mr-1" />
                                {step.department}
                              </span>
                            )}
                            {step.assignedStaff && (
                              <span className="text-xs text-gray-500">
                                {step.assignedStaff}
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          {/* Status Badge */}
                          <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(step.status)}`}>
                            {getStatusIcon(step.status)}
                            <span className="ml-1">{step.status.replace('-', ' ')}</span>
                          </span>

                          {/* Action Buttons */}
                          {step.status === 'in-progress' && (
                            <div className="flex space-x-1">
                              <button
                                onClick={() => updateStepStatus(step.id, 'completed')}
                                className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700"
                              >
                                Complete
                              </button>
                              <button
                                onClick={() => setShowStepDetails(step.id)}
                                className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700"
                              >
                                <Edit size={12} />
                              </button>
                            </div>
                          )}

                          {step.status === 'pending' && index === journeyData.currentStep && (
                            <button
                              onClick={() => updateStepStatus(step.id, 'in-progress')}
                              className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700"
                            >
                              Start
                            </button>
                          )}

                          <button
                            onClick={() => setShowStepDetails(showStepDetails === step.id ? null : step.id)}
                            className="text-gray-400 hover:text-gray-600 p-1"
                          >
                            <Eye size={16} />
                          </button>
                        </div>
                      </div>

                      {/* Step Details */}
                      {showStepDetails === step.id && (
                        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">Department:</span>
                              <span className="ml-2 text-gray-600">{step.department || 'N/A'}</span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Assigned Staff:</span>
                              <span className="ml-2 text-gray-600">{step.assignedStaff || 'N/A'}</span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Estimated Time:</span>
                              <span className="ml-2 text-gray-600">{step.estimatedTime}</span>
                            </div>
                            {step.completedAt && (
                              <div>
                                <span className="font-medium text-gray-700">Completed At:</span>
                                <span className="ml-2 text-gray-600">
                                  {new Date(step.completedAt).toLocaleString()}
                                </span>
                              </div>
                            )}
                          </div>
                          {step.notes && (
                            <div className="mt-3">
                              <span className="font-medium text-gray-700">Notes:</span>
                              <p className="text-gray-600 mt-1">{step.notes}</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      {journeyData && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Activity className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <span className="text-sm font-medium">Record Vitals</span>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <FileText className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <span className="text-sm font-medium">Add Notes</span>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Pill className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <span className="text-sm font-medium">Prescribe</span>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Calendar className="w-6 h-6 text-orange-600 mx-auto mb-2" />
              <span className="text-sm font-medium">Schedule Follow-up</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
