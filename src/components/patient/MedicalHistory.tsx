import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Calendar, 
  Clock, 
  AlertTriangle, 
  Heart, 
  Pill, 
  Activity, 
  FileText, 
  User,
  Plus,
  Edit,
  Eye,
  Loader,
  AlertCircle,
  TrendingUp,
  Stethoscope,
  Users
} from 'lucide-react';
import { patientAPI, patientVisitAPI } from '../../services/apiService';

interface MedicalHistoryItem {
  _id: string;
  type: 'allergy' | 'condition' | 'medication' | 'surgery' | 'visit' | 'family_history';
  date: string;
  title: string;
  description: string;
  status?: string;
  severity?: string;
  doctor?: string;
  department?: string;
  details?: any;
}

interface Patient {
  _id: string;
  patientId: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  bloodType?: string;
  medicalHistory: {
    allergies: Array<{
      allergen: string;
      reaction: string;
      severity: 'Mild' | 'Moderate' | 'Severe';
      dateIdentified?: string;
    }>;
    chronicConditions: Array<{
      condition: string;
      diagnosedDate?: string;
      status: 'Active' | 'Controlled' | 'Resolved';
      notes?: string;
    }>;
    medications: Array<{
      name: string;
      dosage: string;
      frequency: string;
      startDate?: string;
      endDate?: string;
      prescribedBy?: string;
      status: 'Active' | 'Discontinued' | 'Completed';
    }>;
    surgeries: Array<{
      procedure: string;
      date: string;
      hospital?: string;
      surgeon?: string;
      complications?: string;
      outcome?: string;
    }>;
    familyHistory: Array<{
      relationship: string;
      condition: string;
      ageAtDiagnosis?: number;
      notes?: string;
    }>;
  };
}

export function MedicalHistory() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [medicalTimeline, setMedicalTimeline] = useState<MedicalHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [activeTab, setActiveTab] = useState('timeline');

  useEffect(() => {
    fetchPatients();
  }, []);

  useEffect(() => {
    if (selectedPatient) {
      buildMedicalTimeline(selectedPatient);
    }
  }, [selectedPatient]);

  const fetchPatients = async () => {
    try {
      setLoading(true);
      const response = await patientAPI.getAll({ limit: 50 });
      setPatients(response.data || []);
      if (response.data && response.data.length > 0) {
        setSelectedPatient(response.data[0]);
      }
    } catch (err) {
      setError('Failed to fetch patients');
      console.error('Error fetching patients:', err);
    } finally {
      setLoading(false);
    }
  };

  const buildMedicalTimeline = (patient: Patient) => {
    const timeline: MedicalHistoryItem[] = [];

    // Add allergies
    patient.medicalHistory?.allergies?.forEach((allergy, index) => {
      timeline.push({
        _id: `allergy-${index}`,
        type: 'allergy',
        date: allergy.dateIdentified || patient.createdAt || new Date().toISOString(),
        title: `Allergy: ${allergy.allergen}`,
        description: `Reaction: ${allergy.reaction}`,
        severity: allergy.severity,
        details: allergy
      });
    });

    // Add chronic conditions
    patient.medicalHistory?.chronicConditions?.forEach((condition, index) => {
      timeline.push({
        _id: `condition-${index}`,
        type: 'condition',
        date: condition.diagnosedDate || patient.createdAt || new Date().toISOString(),
        title: `Condition: ${condition.condition}`,
        description: condition.notes || 'No additional notes',
        status: condition.status,
        details: condition
      });
    });

    // Add medications
    patient.medicalHistory?.medications?.forEach((medication, index) => {
      timeline.push({
        _id: `medication-${index}`,
        type: 'medication',
        date: medication.startDate || patient.createdAt || new Date().toISOString(),
        title: `Medication: ${medication.name}`,
        description: `${medication.dosage} - ${medication.frequency}`,
        status: medication.status,
        doctor: medication.prescribedBy,
        details: medication
      });
    });

    // Add surgeries
    patient.medicalHistory?.surgeries?.forEach((surgery, index) => {
      timeline.push({
        _id: `surgery-${index}`,
        type: 'surgery',
        date: surgery.date,
        title: `Surgery: ${surgery.procedure}`,
        description: surgery.outcome || 'Surgery completed',
        doctor: surgery.surgeon,
        details: surgery
      });
    });

    // Add family history
    patient.medicalHistory?.familyHistory?.forEach((family, index) => {
      timeline.push({
        _id: `family-${index}`,
        type: 'family_history',
        date: patient.createdAt || new Date().toISOString(),
        title: `Family History: ${family.condition}`,
        description: `${family.relationship} - ${family.condition}`,
        details: family
      });
    });

    // Sort by date (newest first)
    timeline.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    setMedicalTimeline(timeline);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'allergy':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'condition':
        return <Heart className="w-5 h-5 text-orange-500" />;
      case 'medication':
        return <Pill className="w-5 h-5 text-blue-500" />;
      case 'surgery':
        return <Stethoscope className="w-5 h-5 text-purple-500" />;
      case 'visit':
        return <Activity className="w-5 h-5 text-green-500" />;
      case 'family_history':
        return <Users className="w-5 h-5 text-gray-500" />;
      default:
        return <FileText className="w-5 h-5 text-gray-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'allergy':
        return 'bg-red-100 border-red-200 dark:bg-red-900 dark:border-red-700';
      case 'condition':
        return 'bg-orange-100 border-orange-200 dark:bg-orange-900 dark:border-orange-700';
      case 'medication':
        return 'bg-blue-100 border-blue-200 dark:bg-blue-900 dark:border-blue-700';
      case 'surgery':
        return 'bg-purple-100 border-purple-200 dark:bg-purple-900 dark:border-purple-700';
      case 'visit':
        return 'bg-green-100 border-green-200 dark:bg-green-900 dark:border-green-700';
      case 'family_history':
        return 'bg-gray-100 border-gray-200 dark:bg-gray-900 dark:border-gray-700';
      default:
        return 'bg-gray-100 border-gray-200 dark:bg-gray-900 dark:border-gray-700';
    }
  };

  const getSeverityColor = (severity?: string) => {
    switch (severity) {
      case 'Severe':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'Moderate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'Mild':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const filteredTimeline = medicalTimeline.filter(item => {
    if (filterType !== 'all' && item.type !== filterType) return false;
    if (searchTerm && !item.title.toLowerCase().includes(searchTerm.toLowerCase()) && 
        !item.description.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Medical History</h1>
          <p className="text-gray-600 mt-1">Comprehensive patient medical history and timeline</p>
        </div>
      </div>

      {/* Patient Selection */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">Select Patient</label>
            <select
              value={selectedPatient?._id || ''}
              onChange={(e) => {
                const patient = patients.find(p => p._id === e.target.value);
                setSelectedPatient(patient || null);
              }}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a patient...</option>
              {patients.map((patient) => (
                <option key={patient._id} value={patient._id}>
                  {patient.firstName} {patient.lastName} - {patient.patientId}
                </option>
              ))}
            </select>
          </div>
          {selectedPatient && (
            <div className="text-sm text-gray-600">
              <div><strong>DOB:</strong> {new Date(selectedPatient.dateOfBirth).toLocaleDateString()}</div>
              <div><strong>Gender:</strong> {selectedPatient.gender}</div>
              {selectedPatient.bloodType && <div><strong>Blood Type:</strong> {selectedPatient.bloodType}</div>}
            </div>
          )}
        </div>
      </div>

      {selectedPatient && (
        <>
          {/* Tabs */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {[
                  { id: 'timeline', label: 'Medical Timeline', icon: Clock },
                  { id: 'allergies', label: 'Allergies', icon: AlertTriangle },
                  { id: 'conditions', label: 'Conditions', icon: Heart },
                  { id: 'medications', label: 'Medications', icon: Pill },
                  { id: 'surgeries', label: 'Surgeries', icon: Stethoscope },
                  { id: 'family', label: 'Family History', icon: Users }
                ].map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <Icon size={16} />
                      <span>{tab.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Search and Filters */}
            {activeTab === 'timeline' && (
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center space-x-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                      type="text"
                      placeholder="Search medical history..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">All Types</option>
                    <option value="allergy">Allergies</option>
                    <option value="condition">Conditions</option>
                    <option value="medication">Medications</option>
                    <option value="surgery">Surgeries</option>
                    <option value="family_history">Family History</option>
                  </select>
                </div>
              </div>
            )}

            {/* Content */}
            <div className="p-6">
              {activeTab === 'timeline' && (
                <div className="space-y-4">
                  {filteredTimeline.length > 0 ? (
                    filteredTimeline.map((item) => (
                      <div
                        key={item._id}
                        className={`p-4 rounded-lg border-l-4 ${getTypeColor(item.type)}`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3">
                            {getTypeIcon(item.type)}
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <h3 className="text-sm font-medium text-gray-900">{item.title}</h3>
                                {item.severity && (
                                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(item.severity)}`}>
                                    {item.severity}
                                  </span>
                                )}
                                {item.status && (
                                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                                    item.status === 'Active' ? 'bg-green-100 text-green-800' :
                                    item.status === 'Controlled' ? 'bg-blue-100 text-blue-800' :
                                    'bg-gray-100 text-gray-800'
                                  }`}>
                                    {item.status}
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                              {item.doctor && (
                                <p className="text-xs text-gray-500 mt-1">
                                  <User size={12} className="inline mr-1" />
                                  {item.doctor}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-500">
                              {new Date(item.date).toLocaleDateString()}
                            </div>
                            <div className="flex items-center space-x-1 mt-1">
                              <button className="text-blue-600 hover:text-blue-800 p-1">
                                <Eye size={14} />
                              </button>
                              <button className="text-green-600 hover:text-green-800 p-1">
                                <Edit size={14} />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-12">
                      <FileText className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No medical history found</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        No medical history records match your current filters.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Other tab contents would go here */}
              {activeTab !== 'timeline' && (
                <div className="text-center py-12">
                  <div className="text-gray-500">
                    {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} view coming soon...
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {!selectedPatient && !loading && (
        <div className="text-center py-12">
          <User className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No patient selected</h3>
          <p className="mt-1 text-sm text-gray-500">
            Please select a patient to view their medical history.
          </p>
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
