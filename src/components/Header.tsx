import React, { useState, useRef, useEffect } from 'react';
import { Menu, Search, User, Settings, LogOut, ChevronDown, Sun, Moon, Monitor } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';
import { NotificationCenter } from './NotificationCenter';

interface HeaderProps {
  onMenuToggle: () => void;
}

export function Header({ onMenuToggle }: HeaderProps) {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const navigate = useNavigate();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return Sun;
      case 'dark':
        return Moon;
      case 'auto':
        return Monitor;
      default:
        return Sun;
    }
  };

  const ThemeIcon = getThemeIcon();

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  return (
    <header className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm shadow-lg border-b border-slate-200 dark:border-gray-700 px-6 py-4 sticky top-0 z-40">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onMenuToggle}
            className="p-2 hover:bg-slate-100 dark:hover:bg-gray-700 rounded-xl transition-colors"
          >
            <Menu size={20} className="text-slate-600 dark:text-gray-300" />
          </button>

          <div className="relative">
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-gray-400" />
            <input
              type="text"
              placeholder="Search patients, staff, or records..."
              className="pl-10 pr-4 py-2.5 w-96 border border-slate-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-slate-50 dark:bg-gray-800 focus:bg-white dark:focus:bg-gray-700 transition-colors text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
            />
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Real-time Notification Center */}
          <NotificationCenter />

          {/* Theme Toggle Button */}
          <button
            onClick={toggleTheme}
            className="p-2.5 hover:bg-slate-100 dark:hover:bg-gray-700 rounded-xl transition-colors group"
            title={`Current theme: ${theme}`}
          >
            <ThemeIcon size={18} className="text-slate-600 dark:text-gray-300 group-hover:text-slate-800 dark:group-hover:text-gray-100" />
          </button>

          <button className="p-2.5 hover:bg-slate-100 dark:hover:bg-gray-700 rounded-xl transition-colors group">
            <Settings size={18} className="text-slate-600 dark:text-gray-300 group-hover:text-slate-800 dark:group-hover:text-gray-100" />
          </button>

          {/* Replace the user info dropdown trigger with the sidebar's user info block */}
          <div className="relative" ref={userMenuRef}>
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-3 pl-4 border-l border-slate-200 dark:border-gray-600 hover:bg-slate-50 dark:hover:bg-gray-700 rounded-xl p-2 transition-colors"
            >
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <User size={20} className="text-white" />
              </div>
              <div className="flex-1 min-w-0 text-left">
                <div className="font-medium text-slate-900 dark:text-gray-100 truncate">
                  {user?.firstName} {user?.lastName}
                </div>
                <div className="text-sm text-slate-500 dark:text-gray-400 truncate">{user?.role?.name}</div>
              </div>
              <ChevronDown size={14} className={`text-slate-400 dark:text-gray-400 transition-transform ${showUserMenu ? 'rotate-180' : ''}`} />
            </button>
            {/* User Dropdown Menu */}
            {showUserMenu && (
              <div className="absolute right-0 mt-3 w-52 bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-slate-200 dark:border-gray-600 py-2 z-50 backdrop-blur-sm">
                <div className="px-4 py-3 border-b border-slate-100 dark:border-gray-700">
                  <div className="text-sm font-semibold text-slate-900 dark:text-gray-100">
                    {user?.firstName} {user?.lastName}
                  </div>
                  <div className="text-xs text-slate-500 dark:text-gray-400 mt-1">{user?.email}</div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 font-medium mt-1">{user?.role?.name}</div>
                </div>

                <button
                  onClick={() => {
                    navigate('/profile');
                    setShowUserMenu(false);
                  }}
                  className="w-full text-left px-4 py-2.5 text-sm text-slate-700 dark:text-gray-300 hover:bg-slate-50 dark:hover:bg-gray-700 flex items-center transition-colors group"
                >
                  <User size={16} className="mr-3 text-slate-400 dark:text-gray-400 group-hover:text-slate-600 dark:group-hover:text-gray-200" />
                  <span className="font-medium">Profile</span>
                </button>

                <button
                  onClick={() => {
                    navigate('/settings');
                    setShowUserMenu(false);
                  }}
                  className="w-full text-left px-4 py-2.5 text-sm text-slate-700 dark:text-gray-300 hover:bg-slate-50 dark:hover:bg-gray-700 flex items-center transition-colors group"
                >
                  <Settings size={16} className="mr-3 text-slate-400 dark:text-gray-400 group-hover:text-slate-600 dark:group-hover:text-gray-200" />
                  <span className="font-medium">Settings</span>
                </button>

                <div className="border-t border-slate-100 dark:border-gray-700 mt-2 pt-2">
                  <button
                    onClick={() => {
                      handleLogout();
                      setShowUserMenu(false);
                    }}
                    className="w-full text-left px-4 py-2.5 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center transition-colors group"
                  >
                    <LogOut size={16} className="mr-3 text-red-400 group-hover:text-red-600 dark:group-hover:text-red-300" />
                    <span className="font-medium">Sign Out</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}