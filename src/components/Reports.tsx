import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Calendar,
  Download,
  FileText,
  Activity,
  Target,
  AlertCircle,
  RefreshCw,
  Eye,
  Loader,
  Settings,
  TestTube
} from 'lucide-react';
import { reportsAPI } from '../services/apiService';

// Icon mapping for dynamic categories
const getIconComponent = (iconName: string) => {
  switch (iconName) {
    case 'Activity': return Activity;
    case 'DollarSign': return DollarSign;
    case 'TestTube': return TestTube;
    case 'Settings': return Settings;
    case 'Target': return Target;
    case 'BarChart3': return BarChart3;
    case 'TrendingUp': return TrendingUp;
    case 'Pill': return Activity; // Fallback for Pill icon
    default: return FileText;
  }
};



export function Reports() {
  const [reportCategories, setReportCategories] = useState<any[]>([]);
  const [activeCategory, setActiveCategory] = useState('operational');
  const [reportData, setReportData] = useState<Record<string, unknown> | null>(null);
  const [loading, setLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('30');

  const selectedCategory = reportCategories.find(cat => cat.id === activeCategory);

  // Fetch dynamic report categories
  useEffect(() => {
    fetchReportCategories();
  }, []);

  const fetchReportCategories = async () => {
    try {
      setCategoriesLoading(true);
      const response = await reportsAPI.getCategories();
      if (response.success) {
        setReportCategories(response.data);
        // Set first category as active if available
        if (response.data.length > 0) {
          setActiveCategory(response.data[0].id);
        }
      }
    } catch (err) {
      console.error('Error fetching report categories:', err);
      setError('Failed to load report categories');
    } finally {
      setCategoriesLoading(false);
    }
  };

  const fetchReportData = async (categoryId: string, period: string = '30') => {
    setLoading(true);
    setError('');

    try {
      const category = reportCategories.find(cat => cat.id === categoryId);
      if (!category?.apiEndpoint) return;

      let data;
      switch (category.apiEndpoint) {
        case 'getOperationalReports':
          data = await reportsAPI.getOperationalReports(period);
          break;
        case 'getClinicalReports':
          data = await reportsAPI.getClinicalReports(period);
          break;
        case 'getFinancialAnalytics':
          data = await reportsAPI.getFinancialAnalytics(period);
          break;
        case 'getQualityReports':
          data = await reportsAPI.getQualityReports(period);
          break;
        default:
          data = await reportsAPI.getDashboard();
      }

      setReportData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch report data');
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async (report: { name: string; type: string; frequency: string }) => {
    setLoading(true);
    try {
      await fetchReportData(activeCategory, selectedPeriod);
      // Report generated successfully - data is now in reportData state
      console.log('Generated report:', report.name);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate report');
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async (format: string) => {
    try {
      setLoading(true);
      await reportsAPI.exportReport(activeCategory, format, { period: selectedPeriod });
      // In a real implementation, this would trigger a download
      alert(`Report exported as ${format.toUpperCase()}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export report');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReportData(activeCategory, selectedPeriod);
  }, [activeCategory, selectedPeriod]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
        <div className="flex space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 3 months</option>
            <option value="365">Last year</option>
          </select>
          <button
            onClick={() => fetchReportData(activeCategory, selectedPeriod)}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <RefreshCw size={20} className={loading ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
          <button
            onClick={() => exportReport('pdf')}
            disabled={loading}
            className="bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Download size={20} />
            <span>Export PDF</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertCircle size={20} className="text-red-500" />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Dynamic KPI Dashboard */}
      {reportData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {Object.entries((reportData as Record<string, Record<string, unknown>>).data || {}).slice(0, 4).map(([key, value]: [string, unknown]) => (
            <div key={key} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                    <BarChart3 size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </h3>
                    <p className="text-2xl font-bold text-gray-900">
                      {Array.isArray(value)
                        ? value.length
                        : typeof value === 'object' && value !== null
                          ? Object.keys(value).length
                          : String(value || 0)
                      }
                    </p>
                  </div>
                </div>
              </div>
              <div className="text-sm text-gray-500">
                Updated {new Date().toLocaleDateString()}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Analytics Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Chart Placeholder */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Monthly Trends</h2>
            <Calendar size={20} className="text-gray-500" />
          </div>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 size={48} className="text-gray-300 mx-auto mb-2" />
              <p className="text-gray-500">Interactive chart visualization</p>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Performance Metrics</h2>
            <TrendingUp size={20} className="text-gray-500" />
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Bed Occupancy Rate</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '87%' }}></div>
                </div>
                <span className="text-sm font-medium">87%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Surgery Success Rate</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '96%' }}></div>
                </div>
                <span className="text-sm font-medium">96%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Staff Efficiency</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full" style={{ width: '82%' }}></div>
                </div>
                <span className="text-sm font-medium">82%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Equipment Utilization</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                </div>
                <span className="text-sm font-medium">75%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Report Categories */}
      <div className="bg-white rounded-lg shadow">
        {categoriesLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader className="animate-spin mr-2" size={20} />
            <span className="text-gray-500">Loading report categories...</span>
          </div>
        ) : reportCategories.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto mb-2 text-gray-400" size={32} />
            <p className="text-gray-500">No report categories available</p>
          </div>
        ) : (
          <>
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {reportCategories.map((category) => {
                  const IconComponent = getIconComponent(category.icon);
                  return (
                    <button
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeCategory === category.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <IconComponent size={16} />
                        <span>{category.title}</span>
                        <span className="ml-1 bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                          {category.reportCount}
                        </span>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>

            <div className="p-6">
              {selectedCategory && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 ${selectedCategory.color} rounded-lg flex items-center justify-center`}>
                      {React.createElement(getIconComponent(selectedCategory.icon), { size: 20, className: "text-white" })}
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{selectedCategory.title}</h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{selectedCategory.description}</p>
                      <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        Last updated: {new Date(selectedCategory.lastGenerated).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedCategory.reports?.map((report: any) => (
                      <div key={report.name} className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                        <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{report.name}</h3>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Calendar size={14} />
                            <span>{report.frequency}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <FileText size={14} />
                            <span>Type: {report.type}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => generateReport(report)}
                          disabled={loading}
                          className="p-2 hover:bg-gray-200 disabled:bg-gray-100 rounded-lg transition-colors"
                          title="Generate Report"
                        >
                          {loading ? <Loader size={16} className="text-gray-500 animate-spin" /> : <Eye size={16} className="text-gray-500" />}
                        </button>
                        <button
                          onClick={() => exportReport('pdf')}
                          disabled={loading}
                          className="p-2 hover:bg-gray-200 disabled:bg-gray-100 rounded-lg transition-colors"
                          title="Export as PDF"
                        >
                          <Download size={16} className="text-gray-500" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}