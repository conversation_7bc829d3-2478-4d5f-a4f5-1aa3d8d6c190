import React, { useState, useEffect } from 'react';
import {
  Key,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  AlertTriangle,
  CheckCircle,
  Lock,
  Unlock
} from 'lucide-react';
import { adminAPI } from '../../services/apiService';
import { PermissionModal } from './modals/PermissionModal';
import { ConfirmDialog } from './modals/ConfirmDialog';

interface Permission {
  _id: string;
  module: string;
  action: string;
  resource: string;
  description: string;
  category: string;
  isSystemPermission: boolean;
  createdAt: string;
}

export function PermissionManagement() {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Modal states
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [moduleFilter, setModuleFilter] = useState('all');

  useEffect(() => {
    fetchPermissions();
  }, []);

  const fetchPermissions = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getAllPermissions();
      if (response.success) {
        setPermissions(response.data);
      } else {
        setError(response.error || 'Failed to fetch permissions');
      }
    } catch (err) {
      setError('Failed to fetch permissions');
      console.error('Fetch permissions error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePermission = () => {
    setSelectedPermission(null);
    setShowPermissionModal(true);
  };

  const handleEditPermission = (permission: Permission) => {
    if (permission.isSystemPermission) {
      setError('System permissions cannot be edited');
      return;
    }
    setSelectedPermission(permission);
    setShowPermissionModal(true);
  };

  const handleDeletePermission = (permission: Permission) => {
    if (permission.isSystemPermission) {
      setError('System permissions cannot be deleted');
      return;
    }
    setSelectedPermission(permission);
    setShowDeleteDialog(true);
  };

  const confirmDeletePermission = async () => {
    if (!selectedPermission) return;

    try {
      const response = await adminAPI.deletePermission(selectedPermission._id);
      if (response.success) {
        setSuccess('Permission deleted successfully');
        fetchPermissions();
      } else {
        setError(response.error || 'Failed to delete permission');
      }
    } catch (err) {
      setError('Failed to delete permission');
    } finally {
      setShowDeleteDialog(false);
      setSelectedPermission(null);
    }
  };

  const handlePermissionSaved = () => {
    setShowPermissionModal(false);
    setSelectedPermission(null);
    fetchPermissions();
    setSuccess('Permission saved successfully');
  };

  // Get unique categories and modules for filters
  const categories = [...new Set(permissions.map(p => p.category))].sort();
  const modules = [...new Set(permissions.map(p => p.module))].sort();

  // Filter permissions
  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = searchTerm === '' || 
      permission.module.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = categoryFilter === 'all' || permission.category === categoryFilter;
    const matchesModule = moduleFilter === 'all' || permission.module === moduleFilter;

    return matchesSearch && matchesCategory && matchesModule;
  });

  // Group permissions by category
  const groupedPermissions = filteredPermissions.reduce((groups, permission) => {
    const category = permission.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(permission);
    return groups;
  }, {} as Record<string, Permission[]>);

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Permission Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage system permissions and access controls
          </p>
        </div>
        <button
          onClick={handleCreatePermission}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <Plus size={16} />
          <span>Add Permission</span>
        </button>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle size={20} className="text-green-500 dark:text-green-400" />
            <span className="text-green-700 dark:text-green-300">{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle size={20} className="text-red-500 dark:text-red-400" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search permissions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </div>
          <div className="flex gap-3">
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            <select
              value={moduleFilter}
              onChange={(e) => setModuleFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="all">All Modules</option>
              {modules.map(module => (
                <option key={module} value={module}>{module}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Permissions */}
      {loading ? (
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-gray-200 dark:bg-gray-700 h-24 rounded-lg animate-pulse"></div>
          ))}
        </div>
      ) : filteredPermissions.length === 0 ? (
        <div className="text-center py-12">
          <Key size={48} className="text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            No permissions found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {searchTerm || categoryFilter !== 'all' || moduleFilter !== 'all' 
              ? 'Try adjusting your filters' 
              : 'Get started by creating your first permission'}
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
            <div key={category} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {category} ({categoryPermissions.length})
                </h3>
              </div>
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {categoryPermissions.map((permission) => (
                  <div key={permission._id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <div className="flex items-center justify-between">
                      <div className="flex items-start space-x-4">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          permission.isSystemPermission 
                            ? 'bg-purple-100 dark:bg-purple-900/30' 
                            : 'bg-blue-100 dark:bg-blue-900/30'
                        }`}>
                          {permission.isSystemPermission ? (
                            <Lock size={20} className="text-purple-600 dark:text-purple-400" />
                          ) : (
                            <Key size={20} className="text-blue-600 dark:text-blue-400" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                              {permission.module}:{permission.action}:{permission.resource}
                            </h4>
                            {permission.isSystemPermission && (
                              <span className="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300">
                                System
                              </span>
                            )}
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                            {permission.description}
                          </p>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Created {new Date(permission.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      
                      {!permission.isSystemPermission && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEditPermission(permission)}
                            className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDeletePermission(permission)}
                            className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modals */}
      {showPermissionModal && (
        <PermissionModal
          permission={selectedPermission}
          onClose={() => setShowPermissionModal(false)}
          onSave={handlePermissionSaved}
        />
      )}

      {showDeleteDialog && selectedPermission && (
        <ConfirmDialog
          title="Delete Permission"
          message={`Are you sure you want to delete the permission "${selectedPermission.module}:${selectedPermission.action}:${selectedPermission.resource}"? This action cannot be undone.`}
          confirmText="Delete"
          confirmButtonClass="bg-red-500 hover:bg-red-600"
          onConfirm={confirmDeletePermission}
          onCancel={() => setShowDeleteDialog(false)}
        />
      )}
    </div>
  );
}
