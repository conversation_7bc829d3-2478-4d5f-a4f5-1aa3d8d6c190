import React, { useState, useEffect } from 'react';
import { X, Key, Save, AlertTriangle } from 'lucide-react';
import { adminAPI } from '../../../services/apiService';

interface Permission {
  _id: string;
  module: string;
  action: string;
  resource: string;
  description: string;
  category: string;
  isSystemPermission: boolean;
}

interface PermissionModalProps {
  permission: Permission | null;
  onClose: () => void;
  onSave: () => void;
}

const PERMISSION_CATEGORIES = [
  'Dashboard',
  'User Management',
  'Role Management',
  'Patient Management',
  'Appointment Management',
  'Clinical Management',
  'Laboratory Management',
  'Pharmacy Management',
  'Financial Management',
  'Human Resources',
  'Facility Management',
  'System Administration',
  'Reports'
];

const COMMON_MODULES = [
  'dashboard',
  'users',
  'roles',
  'permissions',
  'patients',
  'appointments',
  'clinical',
  'laboratory',
  'pharmacy',
  'financial',
  'hr',
  'facility',
  'reports',
  'admin'
];

const COMMON_ACTIONS = [
  'view',
  'create',
  'edit',
  'delete',
  'export',
  'import',
  'approve',
  'assign',
  'manage'
];

export function PermissionModal({ permission, onClose, onSave }: PermissionModalProps) {
  const [formData, setFormData] = useState({
    module: '',
    action: '',
    resource: '',
    description: '',
    category: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (permission) {
      setFormData({
        module: permission.module || '',
        action: permission.action || '',
        resource: permission.resource || '',
        description: permission.description || '',
        category: permission.category || ''
      });
    }
  }, [permission]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.module.trim()) {
      errors.module = 'Module is required';
    }

    if (!formData.action.trim()) {
      errors.action = 'Action is required';
    }

    if (!formData.resource.trim()) {
      errors.resource = 'Resource is required';
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    }

    if (!formData.category.trim()) {
      errors.category = 'Category is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let response;
      if (permission) {
        response = await adminAPI.updatePermission(permission._id, formData);
      } else {
        response = await adminAPI.createPermission(formData);
      }

      if (response.success) {
        onSave();
      } else {
        setError(response.error || 'Failed to save permission');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to save permission');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            {permission ? 'Edit Permission' : 'Create New Permission'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle size={20} className="text-red-500 dark:text-red-400" />
                <span className="text-red-700 dark:text-red-300">{error}</span>
              </div>
            </div>
          )}

          {/* Permission Structure */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Module *
              </label>
              <input
                type="text"
                list="modules"
                value={formData.module}
                onChange={(e) => handleInputChange('module', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                  validationErrors.module ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="e.g., users, patients"
              />
              <datalist id="modules">
                {COMMON_MODULES.map(module => (
                  <option key={module} value={module} />
                ))}
              </datalist>
              {validationErrors.module && (
                <p className="text-red-500 text-sm mt-1">{validationErrors.module}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Action *
              </label>
              <input
                type="text"
                list="actions"
                value={formData.action}
                onChange={(e) => handleInputChange('action', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                  validationErrors.action ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="e.g., view, create, edit"
              />
              <datalist id="actions">
                {COMMON_ACTIONS.map(action => (
                  <option key={action} value={action} />
                ))}
              </datalist>
              {validationErrors.action && (
                <p className="text-red-500 text-sm mt-1">{validationErrors.action}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Resource *
              </label>
              <input
                type="text"
                value={formData.resource}
                onChange={(e) => handleInputChange('resource', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                  validationErrors.resource ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="e.g., *, own, specific-id"
              />
              {validationErrors.resource && (
                <p className="text-red-500 text-sm mt-1">{validationErrors.resource}</p>
              )}
            </div>
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category *
            </label>
            <select
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                validationErrors.category ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
            >
              <option value="">Select a category</option>
              {PERMISSION_CATEGORIES.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            {validationErrors.category && (
              <p className="text-red-500 text-sm mt-1">{validationErrors.category}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                validationErrors.description ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="Describe what this permission allows"
            />
            {validationErrors.description && (
              <p className="text-red-500 text-sm mt-1">{validationErrors.description}</p>
            )}
          </div>

          {/* Permission Preview */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Permission Preview:
            </h4>
            <div className="flex items-center space-x-2">
              <Key size={16} className="text-blue-500 dark:text-blue-400" />
              <code className="text-sm font-mono text-gray-900 dark:text-gray-100">
                {formData.module || 'module'}:{formData.action || 'action'}:{formData.resource || 'resource'}
              </code>
            </div>
            {formData.category && (
              <div className="mt-2">
                <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded">
                  {formData.category}
                </span>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save size={16} />
              )}
              <span>{loading ? 'Saving...' : 'Save Permission'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
