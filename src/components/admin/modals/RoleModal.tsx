import React, { useState, useEffect } from 'react';
import { X, Shield, Save, AlertTriangle, Key, Check } from 'lucide-react';
import { adminAPI } from '../../../services/apiService';

interface Role {
  _id: string;
  name: string;
  description: string;
  level: number;
  isSystemRole: boolean;
  defaultPermissions: Permission[];
}

interface Permission {
  _id: string;
  module: string;
  action: string;
  resource: string;
  description: string;
  category: string;
}

interface RoleModalProps {
  role: Role | null;
  permissions: Permission[];
  onClose: () => void;
  onSave: () => void;
}

export function RoleModal({ role, permissions, onClose, onSave }: RoleModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    level: 1,
    defaultPermissions: [] as string[]
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (role) {
      setFormData({
        name: role.name || '',
        description: role.description || '',
        level: role.level || 1,
        defaultPermissions: role.defaultPermissions?.map(p => p._id) || []
      });
    }
  }, [role]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Role name is required';
    } else if (formData.name.length < 3) {
      errors.name = 'Role name must be at least 3 characters';
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    }

    if (formData.level < 1 || formData.level > 10) {
      errors.level = 'Level must be between 1 and 10';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let response;
      if (role) {
        response = await adminAPI.updateRole(role._id, formData);
      } else {
        response = await adminAPI.createRole(formData);
      }

      if (response.success) {
        onSave();
      } else {
        setError(response.error || 'Failed to save role');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to save role');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      defaultPermissions: prev.defaultPermissions.includes(permissionId)
        ? prev.defaultPermissions.filter(id => id !== permissionId)
        : [...prev.defaultPermissions, permissionId]
    }));
  };

  const handleSelectAllInCategory = (category: string) => {
    const categoryPermissions = permissions
      .filter(p => category === 'all' || p.category === category)
      .map(p => p._id);
    
    const allSelected = categoryPermissions.every(id => 
      formData.defaultPermissions.includes(id)
    );

    if (allSelected) {
      // Deselect all in category
      setFormData(prev => ({
        ...prev,
        defaultPermissions: prev.defaultPermissions.filter(id => 
          !categoryPermissions.includes(id)
        )
      }));
    } else {
      // Select all in category
      setFormData(prev => ({
        ...prev,
        defaultPermissions: [
          ...prev.defaultPermissions.filter(id => !categoryPermissions.includes(id)),
          ...categoryPermissions
        ]
      }));
    }
  };

  // Group permissions by category (with safety check)
  const safePermissions = Array.isArray(permissions) ? permissions : [];
  const permissionCategories = [...new Set(safePermissions.map(p => p.category))].sort();

  const filteredPermissions = safePermissions.filter(p => {
    const matchesCategory = selectedCategory === 'all' || p.category === selectedCategory;
    const matchesSearch = searchTerm === '' ||
      p.module.toLowerCase().includes(searchTerm.toLowerCase()) ||
      p.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      p.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
      p.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const getCategoryPermissionCount = (category: string) => {
    const categoryPerms = safePermissions.filter(p => p.category === category);
    const selectedCount = categoryPerms.filter(p =>
      formData.defaultPermissions.includes(p._id)
    ).length;
    return `${selectedCount}/${categoryPerms.length}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {role ? 'Edit Role' : 'Create New Role'}
            </h2>
            {role?.isSystemRole && (
              <p className="text-sm text-amber-600 dark:text-amber-400 mt-1">
                System role - Name and level cannot be modified
              </p>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle size={20} className="text-red-500 dark:text-red-400" />
                <span className="text-red-700 dark:text-red-300">{error}</span>
              </div>
            </div>
          )}

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Role Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                disabled={role?.isSystemRole}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                  validationErrors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                } ${role?.isSystemRole ? 'opacity-50 cursor-not-allowed' : ''}`}
                placeholder="Enter role name"
              />
              {validationErrors.name && (
                <p className="text-red-500 text-sm mt-1">{validationErrors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Level (1-10) *
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={formData.level}
                onChange={(e) => handleInputChange('level', parseInt(e.target.value))}
                disabled={role?.isSystemRole}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                  validationErrors.level ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                } ${role?.isSystemRole ? 'opacity-50 cursor-not-allowed' : ''}`}
                placeholder="Enter role level"
              />
              {validationErrors.level && (
                <p className="text-red-500 text-sm mt-1">{validationErrors.level}</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                validationErrors.description ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="Enter role description"
            />
            {validationErrors.description && (
              <p className="text-red-500 text-sm mt-1">{validationErrors.description}</p>
            )}
          </div>

          {/* Permissions */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Permissions ({formData.defaultPermissions.length}/{permissions.length})
              </h3>
              <div className="flex items-center space-x-3">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Categories</option>
                  {permissionCategories.map(category => (
                    <option key={category} value={category}>
                      {category} ({getCategoryPermissionCount(category)})
                    </option>
                  ))}
                </select>
                <button
                  type="button"
                  onClick={() => handleSelectAllInCategory(selectedCategory)}
                  className="px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                >
                  Toggle All
                </button>
              </div>
            </div>

            {/* Search Input */}
            <div className="mb-4">
              <input
                type="text"
                placeholder="Search permissions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
              />
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg max-h-64 overflow-y-auto">
              <div className="p-4 space-y-2">
                {filteredPermissions.map((permission) => (
                  <label
                    key={permission._id}
                    className="flex items-start space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={formData.defaultPermissions.includes(permission._id)}
                      onChange={() => handlePermissionToggle(permission._id)}
                      className="mt-1 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <Key size={14} className="text-gray-400 flex-shrink-0" />
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {permission.module}:{permission.action}:{permission.resource}
                        </span>
                        <span className="px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded">
                          {permission.category}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {permission.description}
                      </p>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save size={16} />
              )}
              <span>{loading ? 'Saving...' : 'Save Role'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
