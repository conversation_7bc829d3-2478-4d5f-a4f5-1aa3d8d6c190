import React, { useState, useEffect } from 'react';
import {
  Shield,
  Plus,
  Edit,
  Trash2,
  <PERSON>,
  Key,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle,
  Crown,
  Lock
} from 'lucide-react';
import { adminAPI } from '../../services/apiService';
import { RoleModal } from './modals/RoleModal';
import { ConfirmDialog } from './modals/ConfirmDialog';

interface Role {
  _id: string;
  name: string;
  description: string;
  level: number;
  isSystemRole: boolean;
  defaultPermissions: Permission[];
  userCount?: number;
  createdAt: string;
}

interface Permission {
  _id: string;
  module: string;
  action: string;
  resource: string;
  description: string;
  category: string;
}

export function RoleManagement() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Modal states
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  useEffect(() => {
    fetchRoles();
    fetchPermissions();
  }, []);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getAllRoles();
      if (response.success) {
        setRoles(response.data);
      } else {
        setError(response.error || 'Failed to fetch roles');
      }
    } catch (err) {
      setError('Failed to fetch roles');
      console.error('Fetch roles error:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchPermissions = async () => {
    try {
      const response = await adminAPI.getAllPermissions();
      if (response.success) {
        // Handle the response structure: response.data.permissions is the array
        const permissionsArray = response.data.permissions || response.data || [];
        setPermissions(Array.isArray(permissionsArray) ? permissionsArray : []);
      }
    } catch (err) {
      console.error('Fetch permissions error:', err);
    }
  };

  const handleCreateRole = () => {
    setSelectedRole(null);
    setShowRoleModal(true);
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setShowRoleModal(true);
  };

  const handleDeleteRole = (role: Role) => {
    if (role.isSystemRole) {
      setError('System roles cannot be deleted');
      return;
    }
    if (role.userCount && role.userCount > 0) {
      setError('Cannot delete role that is assigned to users');
      return;
    }
    setSelectedRole(role);
    setShowDeleteDialog(true);
  };

  const confirmDeleteRole = async () => {
    if (!selectedRole) return;

    try {
      const response = await adminAPI.deleteRole(selectedRole._id);
      if (response.success) {
        setSuccess('Role deleted successfully');
        fetchRoles();
      } else {
        setError(response.error || 'Failed to delete role');
      }
    } catch (err) {
      setError('Failed to delete role');
    } finally {
      setShowDeleteDialog(false);
      setSelectedRole(null);
    }
  };

  const handleRoleSaved = () => {
    setShowRoleModal(false);
    setSelectedRole(null);
    fetchRoles();
    setSuccess('Role saved successfully');
  };

  const getRoleLevelColor = (level: number) => {
    if (level >= 10) return 'text-purple-600 dark:text-purple-400';
    if (level >= 8) return 'text-red-600 dark:text-red-400';
    if (level >= 6) return 'text-orange-600 dark:text-orange-400';
    if (level >= 4) return 'text-blue-600 dark:text-blue-400';
    return 'text-green-600 dark:text-green-400';
  };

  const getRoleLevelBadge = (level: number) => {
    if (level >= 10) return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300';
    if (level >= 8) return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
    if (level >= 6) return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300';
    if (level >= 4) return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
    return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Role Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage roles and their permissions
          </p>
        </div>
        <button
          onClick={handleCreateRole}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <Plus size={16} />
          <span>Add Role</span>
        </button>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle size={20} className="text-green-500 dark:text-green-400" />
            <span className="text-green-700 dark:text-green-300">{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle size={20} className="text-red-500 dark:text-red-400" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* Roles Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-gray-200 dark:bg-gray-700 h-48 rounded-lg animate-pulse"></div>
          ))}
        </div>
      ) : roles.length === 0 ? (
        <div className="text-center py-12">
          <Shield size={48} className="text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            No roles found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Get started by creating your first role
          </p>
          <button
            onClick={handleCreateRole}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
          >
            Create Role
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {roles.map((role) => (
            <div
              key={role._id}
              className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    role.isSystemRole 
                      ? 'bg-purple-100 dark:bg-purple-900/30' 
                      : 'bg-blue-100 dark:bg-blue-900/30'
                  }`}>
                    {role.isSystemRole ? (
                      <Crown size={20} className="text-purple-600 dark:text-purple-400" />
                    ) : (
                      <Shield size={20} className="text-blue-600 dark:text-blue-400" />
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {role.name}
                    </h3>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleLevelBadge(role.level)}`}>
                        Level {role.level}
                      </span>
                      {role.isSystemRole && (
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                          <Lock size={10} className="inline mr-1" />
                          System
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex space-x-1">
                  <button
                    onClick={() => handleEditRole(role)}
                    className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                    title={role.isSystemRole ? "Edit system role permissions" : "Edit role"}
                  >
                    <Edit size={16} />
                  </button>
                  {!role.isSystemRole && (
                    <button
                      onClick={() => handleDeleteRole(role)}
                      className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <Trash2 size={16} />
                    </button>
                  )}
                </div>
              </div>

              <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                {role.description}
              </p>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Users size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Users</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {role.userCount || 0}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Key size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Permissions</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {role.defaultPermissions?.length || 0}
                  </span>
                </div>

                <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Created {new Date(role.createdAt).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modals */}
      {showRoleModal && (
        <RoleModal
          role={selectedRole}
          permissions={permissions}
          onClose={() => setShowRoleModal(false)}
          onSave={handleRoleSaved}
        />
      )}

      {showDeleteDialog && selectedRole && (
        <ConfirmDialog
          title="Delete Role"
          message={`Are you sure you want to delete the role "${selectedRole.name}"? This action cannot be undone.`}
          confirmText="Delete"
          confirmButtonClass="bg-red-500 hover:bg-red-600"
          onConfirm={confirmDeleteRole}
          onCancel={() => setShowDeleteDialog(false)}
        />
      )}
    </div>
  );
}
