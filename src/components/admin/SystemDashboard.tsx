import React, { useState, useEffect } from 'react';
import {
  Users,
  Shield,
  Key,
  Activity,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Server,
  Cpu
} from 'lucide-react';
import { adminAPI } from '../../services/apiService';

interface SystemStats {
  totalUsers: number;
  activeUsers: number;
  totalRoles: number;
  totalPermissions: number;
  recentLogins: number;
  failedLogins: number;
  systemHealth: {
    status: 'healthy' | 'warning' | 'critical';
    uptime: string;
    memoryUsage: number;
    cpuUsage: number;
    diskUsage: number;
  };
}

interface RecentActivity {
  id: string;
  action: string;
  user: string;
  timestamp: string;
  status: 'success' | 'warning' | 'error';
}

export function SystemDashboard() {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch system statistics
      const statsResponse = await adminAPI.getSystemStats();
      if (statsResponse.success) {
        setStats(statsResponse.data);
      }

      // Fetch recent audit logs for activity
      const auditResponse = await adminAPI.getAuditLogs({ limit: 10 });
      if (auditResponse.success) {
        const activities: RecentActivity[] = auditResponse.data.map((log: any) => ({
          id: log._id,
          action: log.action,
          user: log.user?.firstName + ' ' + log.user?.lastName || 'System',
          timestamp: log.createdAt,
          status: log.action.includes('Failed') ? 'error' : 'success'
        }));
        setRecentActivity(activities);
      }

    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Dashboard error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-500 dark:text-green-400';
      case 'warning':
        return 'text-yellow-500 dark:text-yellow-400';
      case 'critical':
        return 'text-red-500 dark:text-red-400';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle size={16} className="text-green-500 dark:text-green-400" />;
      case 'warning':
        return <AlertTriangle size={16} className="text-yellow-500 dark:text-yellow-400" />;
      case 'critical':
        return <AlertTriangle size={16} className="text-red-500 dark:text-red-400" />;
      default:
        return <Clock size={16} className="text-gray-500 dark:text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-200 dark:bg-gray-700 h-24 rounded-lg"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-200 dark:bg-gray-700 h-64 rounded-lg"></div>
            <div className="bg-gray-200 dark:bg-gray-700 h-64 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="text-center">
          <AlertTriangle size={48} className="text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Failed to Load Dashboard
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={fetchDashboardData}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          System Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Overview of system status and recent activity
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Users</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {stats?.totalUsers || 0}
              </p>
            </div>
            <Users size={24} className="text-blue-500 dark:text-blue-400" />
          </div>
          <div className="flex items-center mt-2">
            <span className="text-sm text-green-600 dark:text-green-400">
              {stats?.activeUsers || 0} active
            </span>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Roles</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {stats?.totalRoles || 0}
              </p>
            </div>
            <Shield size={24} className="text-purple-500 dark:text-purple-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Permissions</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {stats?.totalPermissions || 0}
              </p>
            </div>
            <Key size={24} className="text-green-500 dark:text-green-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Failed Logins</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {stats?.failedLogins || 0}
              </p>
            </div>
            <AlertTriangle size={24} className="text-red-500 dark:text-red-400" />
          </div>
          <div className="flex items-center mt-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Last 24 hours
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Health */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            System Health
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getStatusIcon(stats?.systemHealth?.status || 'healthy')}
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Overall Status
                </span>
              </div>
              <span className={`text-sm font-medium capitalize ${getStatusColor(stats?.systemHealth?.status || 'healthy')}`}>
                {stats?.systemHealth?.status || 'Healthy'}
              </span>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Cpu size={16} className="text-gray-500 dark:text-gray-400" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">CPU Usage</span>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {stats?.systemHealth?.cpuUsage || 0}%
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Database size={16} className="text-gray-500 dark:text-gray-400" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Memory Usage</span>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {stats?.systemHealth?.memoryUsage || 0}%
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Server size={16} className="text-gray-500 dark:text-gray-400" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Disk Usage</span>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {stats?.systemHealth?.diskUsage || 0}%
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock size={16} className="text-gray-500 dark:text-gray-400" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Uptime</span>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {stats?.systemHealth?.uptime || '0 days'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Recent Activity
          </h2>
          
          <div className="space-y-3">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {activity.action}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      by {activity.user} • {new Date(activity.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                No recent activity
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
