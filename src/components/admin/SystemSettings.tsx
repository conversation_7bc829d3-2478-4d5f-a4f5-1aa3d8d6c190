import React, { useState, useEffect } from 'react';
import {
  Settings,
  Save,
  Refresh<PERSON>w,
  <PERSON>ertTriangle,
  CheckCircle,
  Info,
  Shield,
  Mail,
  Bell,
  Database,
  Globe,
  Clock,
  Lock
} from 'lucide-react';
import { adminAPI } from '../../services/apiService';

interface SystemSetting {
  key: string;
  label: string;
  description: string;
  type: 'text' | 'number' | 'boolean' | 'select' | 'password' | 'textarea';
  value: any;
  category: string;
  options?: Array<{ label: string; value: any }>;
  validation?: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: string;
  };
}

const SETTING_CATEGORIES = [
  { id: 'general', label: 'General', icon: Settings },
  { id: 'security', label: 'Security', icon: Shield },
  { id: 'email', label: 'Email', icon: Mail },
  { id: 'notifications', label: 'Notifications', icon: Bell },
  { id: 'backup', label: 'Backup', icon: Database }
];

export function SystemSettings() {
  const [settings, setSettings] = useState<SystemSetting[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState('general');
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getSystemSettings();
      if (response.success) {
        setSettings(response.data);
      } else {
        // Use mock data if API fails
        setSettings(getMockSettings());
      }
    } catch (err) {
      console.error('Fetch settings error:', err);
      setSettings(getMockSettings());
    } finally {
      setLoading(false);
    }
  };

  const getMockSettings = (): SystemSetting[] => [
    // General Settings
    {
      key: 'system_name',
      label: 'System Name',
      description: 'The name of your hospital management system',
      type: 'text',
      value: 'Inception HMS',
      category: 'general',
      validation: { required: true }
    },
    {
      key: 'system_tagline',
      label: 'System Tagline',
      description: 'Tagline displayed throughout the system',
      type: 'text',
      value: 'Intelligent Care, Ancient Wisdom',
      category: 'general'
    },
    {
      key: 'timezone',
      label: 'System Timezone',
      description: 'Default timezone for the system',
      type: 'select',
      value: 'UTC',
      category: 'general',
      options: [
        { label: 'UTC', value: 'UTC' },
        { label: 'America/New_York', value: 'America/New_York' },
        { label: 'Europe/London', value: 'Europe/London' },
        { label: 'Asia/Kolkata', value: 'Asia/Kolkata' },
        { label: 'Asia/Tokyo', value: 'Asia/Tokyo' }
      ]
    },
    {
      key: 'language',
      label: 'Default Language',
      description: 'Default language for the system interface',
      type: 'select',
      value: 'en',
      category: 'general',
      options: [
        { label: 'English', value: 'en' },
        { label: 'Spanish', value: 'es' },
        { label: 'French', value: 'fr' },
        { label: 'German', value: 'de' },
        { label: 'Hindi', value: 'hi' }
      ]
    },

    // Security Settings
    {
      key: 'password_min_length',
      label: 'Minimum Password Length',
      description: 'Minimum number of characters required for passwords',
      type: 'number',
      value: 8,
      category: 'security',
      validation: { min: 6, max: 32 }
    },
    {
      key: 'password_require_uppercase',
      label: 'Require Uppercase Letters',
      description: 'Passwords must contain at least one uppercase letter',
      type: 'boolean',
      value: true,
      category: 'security'
    },
    {
      key: 'session_timeout',
      label: 'Session Timeout (minutes)',
      description: 'Automatic logout after inactivity',
      type: 'number',
      value: 30,
      category: 'security',
      validation: { min: 5, max: 480 }
    },
    {
      key: 'max_login_attempts',
      label: 'Maximum Login Attempts',
      description: 'Number of failed login attempts before account lockout',
      type: 'number',
      value: 5,
      category: 'security',
      validation: { min: 3, max: 10 }
    },

    // Email Settings
    {
      key: 'smtp_host',
      label: 'SMTP Host',
      description: 'SMTP server hostname',
      type: 'text',
      value: 'smtp.gmail.com',
      category: 'email',
      validation: { required: true }
    },
    {
      key: 'smtp_port',
      label: 'SMTP Port',
      description: 'SMTP server port number',
      type: 'number',
      value: 587,
      category: 'email',
      validation: { min: 1, max: 65535 }
    },
    {
      key: 'email_from_name',
      label: 'From Name',
      description: 'Name displayed in outgoing emails',
      type: 'text',
      value: 'Inception Hospital',
      category: 'email'
    },

    // Notification Settings
    {
      key: 'enable_email_notifications',
      label: 'Enable Email Notifications',
      description: 'Send notifications via email',
      type: 'boolean',
      value: true,
      category: 'notifications'
    },
    {
      key: 'notification_retention_days',
      label: 'Notification Retention (days)',
      description: 'How long to keep notification history',
      type: 'number',
      value: 30,
      category: 'notifications',
      validation: { min: 1, max: 365 }
    },

    // Backup Settings
    {
      key: 'auto_backup_enabled',
      label: 'Enable Automatic Backups',
      description: 'Automatically backup system data',
      type: 'boolean',
      value: true,
      category: 'backup'
    },
    {
      key: 'backup_frequency',
      label: 'Backup Frequency',
      description: 'How often to perform automatic backups',
      type: 'select',
      value: 'daily',
      category: 'backup',
      options: [
        { label: 'Hourly', value: 'hourly' },
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' },
        { label: 'Monthly', value: 'monthly' }
      ]
    }
  ];

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => prev.map(setting => 
      setting.key === key ? { ...setting, value } : setting
    ));
    setHasChanges(true);
    setError(null);
    setSuccess(null);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Validate settings
      const errors = [];
      for (const setting of settings) {
        if (setting.validation?.required && !setting.value) {
          errors.push(`${setting.label} is required`);
        }
        if (setting.type === 'number' && setting.validation) {
          const numValue = Number(setting.value);
          if (setting.validation.min && numValue < setting.validation.min) {
            errors.push(`${setting.label} must be at least ${setting.validation.min}`);
          }
          if (setting.validation.max && numValue > setting.validation.max) {
            errors.push(`${setting.label} must be at most ${setting.validation.max}`);
          }
        }
      }

      if (errors.length > 0) {
        setError(errors.join(', '));
        return;
      }

      const response = await adminAPI.updateSystemSettings(settings);
      if (response.success) {
        setSuccess('Settings saved successfully');
        setHasChanges(false);
      } else {
        setError(response.error || 'Failed to save settings');
      }
    } catch (err) {
      setError('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    fetchSettings();
    setHasChanges(false);
    setError(null);
    setSuccess(null);
  };

  const renderSettingInput = (setting: SystemSetting) => {
    switch (setting.type) {
      case 'text':
        return (
          <input
            type="text"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.key, e.target.value)}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            placeholder={setting.description}
          />
        );

      case 'number':
        return (
          <input
            type="number"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.key, parseInt(e.target.value))}
            min={setting.validation?.min}
            max={setting.validation?.max}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          />
        );

      case 'boolean':
        return (
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={setting.value}
              onChange={(e) => handleSettingChange(setting.key, e.target.checked)}
              className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {setting.value ? 'Enabled' : 'Disabled'}
            </span>
          </label>
        );

      case 'select':
        return (
          <select
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.key, e.target.value)}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            {setting.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'password':
        return (
          <input
            type="password"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.key, e.target.value)}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            placeholder="Enter password"
          />
        );

      case 'textarea':
        return (
          <textarea
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.key, e.target.value)}
            rows={3}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            placeholder={setting.description}
          />
        );

      default:
        return null;
    }
  };

  const filteredSettings = settings.filter(setting => setting.category === activeCategory);

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="bg-gray-200 dark:bg-gray-700 h-64 rounded-lg"></div>
            <div className="lg:col-span-3 bg-gray-200 dark:bg-gray-700 h-64 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">System Settings</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Configure system-wide settings and preferences
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleReset}
            disabled={!hasChanges || saving}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={16} />
            <span>Reset</span>
          </button>
          <button
            onClick={handleSave}
            disabled={!hasChanges || saving}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
          >
            {saving ? <RefreshCw size={16} className="animate-spin" /> : <Save size={16} />}
            <span>{saving ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle size={20} className="text-green-500 dark:text-green-400" />
            <span className="text-green-700 dark:text-green-300">{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle size={20} className="text-red-500 dark:text-red-400" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {hasChanges && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Info size={20} className="text-yellow-500 dark:text-yellow-400" />
            <span className="text-yellow-700 dark:text-yellow-300">
              You have unsaved changes. Don't forget to save your settings.
            </span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Category Navigation */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Categories</h3>
            <nav className="space-y-2">
              {SETTING_CATEGORIES.map(category => {
                const Icon = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeCategory === category.id
                        ? 'bg-blue-500 dark:bg-blue-600 text-white'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    <Icon size={18} />
                    <span>{category.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
              {SETTING_CATEGORIES.find(c => c.id === activeCategory)?.label} Settings
            </h3>
            
            <div className="space-y-6">
              {filteredSettings.map(setting => (
                <div key={setting.key} className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {setting.label}
                    {setting.validation?.required && (
                      <span className="text-red-500 dark:text-red-400 ml-1">*</span>
                    )}
                  </label>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    {setting.description}
                  </p>
                  {renderSettingInput(setting)}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
