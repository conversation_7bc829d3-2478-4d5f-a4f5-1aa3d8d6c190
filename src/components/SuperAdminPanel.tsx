import React, { useState, useEffect } from 'react';
import {
  Shield,
  Users,
  Key,
  Settings,
  Database,
  AlertTriangle,
  CheckCircle,
  Loader,
  Plus,
  Edit,
  Trash2,
  UserPlus,
  UserCheck,
  UserX,
  Download,
  Upload,
  RefreshCw
} from 'lucide-react';
import { adminAPI } from '../services/apiService';
import { useAuth } from '../context/AuthContext';

interface Permission {
  _id: string;
  module: string;
  action: string;
  resource: string;
  description: string;
  category: string;
}

interface Role {
  _id: string;
  name: string;
  description: string;
  level: number;
  isSystemRole: boolean;
  defaultPermissions: Permission[];
  userCount?: number;
}

export function SuperAdminPanel() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Data states
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [permissionCategories, setPermissionCategories] = useState<any>({});
  const [users, setUsers] = useState<any[]>([]);
  const [systemStats, setSystemStats] = useState<any>(null);

  // Modal states
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  // Form states
  const [roleFormData, setRoleFormData] = useState({
    name: '',
    description: '',
    level: 1,
    permissions: [] as string[]
  });

  useEffect(() => {
    if (user?.role?.level >= 10) {
      fetchAllData();
    }
  }, [user]);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchRoles(),
        fetchPermissions(),
        fetchUsers(),
        fetchSystemStats()
      ]);
    } catch (err) {
      setError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await adminAPI.getRoles();
      if (response.success) {
        setRoles(response.data);
      }
    } catch (err) {
      console.error('Error fetching roles:', err);
    }
  };

  const fetchPermissions = async () => {
    try {
      const [categoriesResponse, allPermissionsResponse] = await Promise.all([
        adminAPI.getPermissionCategories(),
        adminAPI.getAllPermissions()
      ]);

      if (categoriesResponse.success) {
        setPermissionCategories(categoriesResponse.data);
      }

      if (allPermissionsResponse.success) {
        setPermissions(allPermissionsResponse.data.permissions);
      }
    } catch (err) {
      console.error('Error fetching permissions:', err);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await adminAPI.getUsers();
      if (response.success) {
        setUsers(response.data);
      }
    } catch (err) {
      console.error('Error fetching users:', err);
    }
  };

  const fetchSystemStats = async () => {
    try {
      const response = await adminAPI.getSystemStats();
      if (response.success) {
        setSystemStats(response.data);
      }
    } catch (err) {
      console.error('Error fetching system stats:', err);
    }
  };

  const handleInitializeSystem = async () => {
    if (!confirm('This will initialize/update system permissions and roles. Continue?')) {
      return;
    }

    setLoading(true);
    try {
      const response = await adminAPI.initializeSystem();
      if (response.success) {
        setSuccess('System initialized successfully');
        await fetchAllData();
      } else {
        setError(response.error || 'Failed to initialize system');
      }
    } catch (err) {
      setError('Failed to initialize system');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRole = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const response = await adminAPI.createRole(roleFormData);
      if (response.success) {
        setSuccess('Role created successfully');
        setShowRoleModal(false);
        resetRoleForm();
        await fetchRoles();
      } else {
        setError(response.error || 'Failed to create role');
      }
    } catch (err) {
      setError('Failed to create role');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateRole = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedRole) return;

    setLoading(true);
    try {
      const response = await adminAPI.updateRole(selectedRole._id, roleFormData);
      if (response.success) {
        setSuccess('Role updated successfully');
        setShowRoleModal(false);
        resetRoleForm();
        setSelectedRole(null);
        await fetchRoles();
      } else {
        setError(response.error || 'Failed to update role');
      }
    } catch (err) {
      setError('Failed to update role');
    } finally {
      setLoading(false);
    }
  };

  const handleAssignPermissionsToRole = async (roleId: string, permissionIds: string[]) => {
    setLoading(true);
    try {
      const response = await adminAPI.assignPermissionsToRole(roleId, permissionIds);
      if (response.success) {
        setSuccess('Permissions assigned successfully');
        await fetchRoles();
      } else {
        setError(response.error || 'Failed to assign permissions');
      }
    } catch (err) {
      setError('Failed to assign permissions');
    } finally {
      setLoading(false);
    }
  };

  const handleAssignRoleToUser = async (userId: string, roleId: string) => {
    setLoading(true);
    try {
      const response = await adminAPI.assignRoleToUser(userId, roleId);
      if (response.success) {
        setSuccess('Role assigned to user successfully');
        await fetchUsers();
      } else {
        setError(response.error || 'Failed to assign role');
      }
    } catch (err) {
      setError('Failed to assign role');
    } finally {
      setLoading(false);
    }
  };

  const resetRoleForm = () => {
    setRoleFormData({
      name: '',
      description: '',
      level: 1,
      permissions: []
    });
  };

  const openRoleModal = (role?: Role) => {
    if (role) {
      setSelectedRole(role);
      setRoleFormData({
        name: role.name,
        description: role.description,
        level: role.level,
        permissions: role.defaultPermissions.map(p => p._id)
      });
    } else {
      setSelectedRole(null);
      resetRoleForm();
    }
    setShowRoleModal(true);
  };

  // Check if user is super admin
  if (user?.role?.level < 10) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle size={48} className="text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Access Denied</h2>
          <p className="text-gray-600 dark:text-gray-400">Only Super Administrators can access this panel.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Super Admin Panel</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">Complete system administration and control</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleInitializeSystem}
            disabled={loading}
            className="bg-purple-500 hover:bg-purple-600 dark:bg-purple-600 dark:hover:bg-purple-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
          >
            <Database size={20} />
            <span>Initialize System</span>
          </button>
          <button
            onClick={fetchAllData}
            disabled={loading}
            className="bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={20} className={loading ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle size={20} className="text-green-500 dark:text-green-400" />
            <span className="text-green-700 dark:text-green-300">{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle size={20} className="text-red-500 dark:text-red-400" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Roles</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{roles.length}</p>
            </div>
            <Shield size={24} className="text-purple-500 dark:text-purple-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Permissions</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{permissions.length}</p>
            </div>
            <Key size={24} className="text-blue-500 dark:text-blue-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Active Users</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {users.filter(u => u.isActive).length}
              </p>
            </div>
            <Users size={24} className="text-green-500 dark:text-green-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">System Health</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">99.9%</p>
            </div>
            <CheckCircle size={24} className="text-emerald-500 dark:text-emerald-400" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'System Overview', icon: Settings },
              { id: 'roles', label: 'Role Management', icon: Shield },
              { id: 'permissions', label: 'Permission Management', icon: Key },
              { id: 'users', label: 'User Assignment', icon: Users }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === id
                    ? 'border-purple-500 dark:border-purple-400 text-purple-600 dark:text-purple-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <Icon size={16} />
                <span>{label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {loading && (
            <div className="flex items-center justify-center py-12">
              <Loader className="animate-spin text-purple-500 dark:text-purple-400" size={24} />
              <span className="ml-2 text-gray-500 dark:text-gray-400">Loading...</span>
            </div>
          )}

          {!loading && activeTab === 'overview' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">System Overview</h2>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Permission Categories</h3>
                  <div className="space-y-2">
                    {Object.entries(permissionCategories).map(([category, perms]: [string, any]) => (
                      <div key={category} className="flex justify-between items-center">
                        <span className="text-gray-700 dark:text-gray-300">{category}</span>
                        <span className="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-2 py-1 rounded-full text-sm">
                          {perms.length}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Role Distribution</h3>
                  <div className="space-y-2">
                    {roles.map(role => (
                      <div key={role._id} className="flex justify-between items-center">
                        <span className="text-gray-700 dark:text-gray-300">{role.name}</span>
                        <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-sm">
                          {users.filter(u => u.role?._id === role._id).length} users
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {!loading && activeTab === 'roles' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Role Management</h2>
                <button
                  onClick={() => openRoleModal()}
                  className="bg-purple-500 hover:bg-purple-600 dark:bg-purple-600 dark:hover:bg-purple-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
                >
                  <Plus size={16} />
                  <span>Create Role</span>
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {roles.map(role => (
                  <div key={role._id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 dark:from-purple-600 dark:to-purple-700 rounded-full flex items-center justify-center">
                          <Shield size={18} className="text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-gray-100">{role.name}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Level {role.level}</p>
                        </div>
                      </div>
                      {role.isSystemRole && (
                        <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs">
                          System
                        </span>
                      )}
                    </div>

                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">{role.description}</p>

                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Permissions ({role.defaultPermissions?.length || 0})
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {(role.defaultPermissions || []).slice(0, 3).map((permission, index) => (
                          <span key={index} className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-xs">
                            {permission.module}:{permission.action}
                          </span>
                        ))}
                        {(role.defaultPermissions?.length || 0) > 3 && (
                          <span className="bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 px-2 py-1 rounded text-xs">
                            +{(role.defaultPermissions?.length || 0) - 3} more
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {users.filter(u => u.role?._id === role._id).length} users
                      </span>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => openRoleModal(role)}
                          className="p-2 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-colors group"
                          title="Edit Role"
                        >
                          <Edit size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-purple-600 dark:group-hover:text-purple-400" />
                        </button>
                        {!role.isSystemRole && (
                          <button
                            onClick={() => {
                              if (confirm(`Delete role "${role.name}"? This action cannot be undone.`)) {
                                // Handle delete role
                              }
                            }}
                            className="p-2 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors group"
                            title="Delete Role"
                          >
                            <Trash2 size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {!loading && activeTab === 'permissions' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Permission Management</h2>

              {Object.entries(permissionCategories).map(([category, categoryPermissions]: [string, any]) => (
                <div key={category} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{category}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {categoryPermissions.map((permission: Permission) => (
                      <div key={permission._id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <Key size={16} className="text-blue-500 dark:text-blue-400" />
                          <span className="font-medium text-gray-900 dark:text-gray-100">
                            {permission.module}:{permission.action}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{permission.description}</p>
                        <span className="text-xs text-gray-500 dark:text-gray-500">
                          Resource: {permission.resource}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {!loading && activeTab === 'users' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">User Role Assignment</h2>

              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">User</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Current Role</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Department</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Status</th>
                        <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {users.map(user => (
                        <tr key={user._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 rounded-full flex items-center justify-center">
                                <span className="text-white text-sm font-medium">
                                  {user.firstName?.[0]}{user.lastName?.[0]}
                                </span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 dark:text-gray-100">
                                  {user.firstName} {user.lastName}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <span className="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-2 py-1 rounded-full text-sm">
                              {user.role?.name || 'No Role'}
                            </span>
                          </td>
                          <td className="py-4 px-6 text-sm text-gray-900 dark:text-gray-100">
                            {user.department || 'N/A'}
                          </td>
                          <td className="py-4 px-6">
                            <span className={`px-2 py-1 rounded-full text-sm ${
                              user.isActive
                                ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                                : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                            }`}>
                              {user.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="py-4 px-6">
                            <select
                              value={user.role?._id || ''}
                              onChange={(e) => {
                                if (e.target.value && e.target.value !== user.role?._id) {
                                  handleAssignRoleToUser(user._id, e.target.value);
                                }
                              }}
                              className="border border-gray-300 dark:border-gray-600 rounded px-3 py-1 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                            >
                              <option value="">Select Role</option>
                              {roles.map(role => (
                                <option key={role._id} value={role._id}>{role.name}</option>
                              ))}
                            </select>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Role Creation/Edit Modal */}
      {showRoleModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {selectedRole ? 'Edit Role' : 'Create New Role'}
                </h2>
                <button
                  onClick={() => {
                    setShowRoleModal(false);
                    setSelectedRole(null);
                    resetRoleForm();
                  }}
                  className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <form onSubmit={selectedRole ? handleUpdateRole : handleCreateRole} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Role Name *
                    </label>
                    <input
                      type="text"
                      value={roleFormData.name}
                      onChange={(e) => setRoleFormData(prev => ({ ...prev, name: e.target.value }))}
                      required
                      disabled={selectedRole?.isSystemRole}
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:bg-gray-100 dark:disabled:bg-gray-600"
                    />
                    {selectedRole?.isSystemRole && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">System role names cannot be modified</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Role Level (1-10) *
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="9"
                      value={roleFormData.level}
                      onChange={(e) => setRoleFormData(prev => ({ ...prev, level: parseInt(e.target.value) }))}
                      required
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Higher levels have more authority (10 is reserved for Super Admin)</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Description *
                  </label>
                  <textarea
                    value={roleFormData.description}
                    onChange={(e) => setRoleFormData(prev => ({ ...prev, description: e.target.value }))}
                    required
                    rows={3}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                    Permissions *
                  </label>
                  <div className="space-y-4 max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    {Object.entries(permissionCategories).map(([category, categoryPermissions]: [string, any]) => (
                      <div key={category} className="space-y-2">
                        <h4 className="font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2">
                          {category}
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {categoryPermissions.map((permission: Permission) => (
                            <label key={permission._id} className="flex items-center space-x-2 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
                              <input
                                type="checkbox"
                                checked={roleFormData.permissions.includes(permission._id)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setRoleFormData(prev => ({
                                      ...prev,
                                      permissions: [...prev.permissions, permission._id]
                                    }));
                                  } else {
                                    setRoleFormData(prev => ({
                                      ...prev,
                                      permissions: prev.permissions.filter(p => p !== permission._id)
                                    }));
                                  }
                                }}
                                className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500"
                              />
                              <div className="flex-1">
                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                  {permission.module}:{permission.action}:{permission.resource}
                                </span>
                                <p className="text-xs text-gray-500 dark:text-gray-400">{permission.description}</p>
                              </div>
                            </label>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                    Selected: {roleFormData.permissions.length} permissions
                  </p>
                </div>

                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    type="button"
                    onClick={() => {
                      setShowRoleModal(false);
                      setSelectedRole(null);
                      resetRoleForm();
                    }}
                    className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading || roleFormData.permissions.length === 0}
                    className="px-6 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:bg-purple-300 dark:disabled:bg-purple-700 flex items-center space-x-2"
                  >
                    {loading && <Loader className="animate-spin" size={16} />}
                    <span>{selectedRole ? 'Update Role' : 'Create Role'}</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
