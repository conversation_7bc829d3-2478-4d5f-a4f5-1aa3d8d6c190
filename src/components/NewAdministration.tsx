import React, { useState, useEffect } from 'react';
import {
  Shield,
  Users,
  Key,
  Settings,
  Activity,
  BarChart3,
  AlertTriangle
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';

// Import clean, dedicated components
import { UserManagement } from './admin/UserManagement';
import { RoleManagement } from './admin/RoleManagement';
import { PermissionManagement } from './admin/PermissionManagement';
import { AuditSystem } from './admin/AuditSystem';
import { SystemSettings } from './admin/SystemSettings';
import { SystemDashboard } from './admin/SystemDashboard';

interface AdminTab {
  id: string;
  label: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  component: React.ComponentType<any>;
  requiredLevel?: number;
  description: string;
}

const adminTabs: AdminTab[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: BarChart3,
    component: SystemDashboard,
    description: 'System overview and statistics'
  },
  {
    id: 'users',
    label: 'User Management',
    icon: Users,
    component: UserManagement,
    description: 'Manage user accounts and profiles'
  },
  {
    id: 'roles',
    label: 'Role Management',
    icon: Shield,
    component: RoleManagement,
    requiredLevel: 8,
    description: 'Manage roles and role hierarchy'
  },
  {
    id: 'permissions',
    label: 'Permissions',
    icon: Key,
    component: PermissionManagement,
    requiredLevel: 9,
    description: 'Manage system permissions'
  },
  {
    id: 'audit',
    label: 'Audit Logs',
    icon: Activity,
    component: AuditSystem,
    description: 'View system audit logs and activity'
  },
  {
    id: 'settings',
    label: 'System Settings',
    icon: Settings,
    component: SystemSettings,
    requiredLevel: 10,
    description: 'Configure system settings'
  }
];

export function Administration() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize admin section
    const initializeAdmin = async () => {
      try {
        setLoading(true);
        // Check if user has admin access
        if (!user?.role || user.role.level < 5) {
          setError('Access denied. Insufficient permissions.');
          return;
        }
        
        // Simulate initialization
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (err) {
        setError('Failed to initialize administration panel');
      } finally {
        setLoading(false);
      }
    };

    initializeAdmin();
  }, [user]);

  // Filter tabs based on user permissions
  const availableTabs = adminTabs.filter(tab => {
    if (!tab.requiredLevel) return true;
    return user?.role?.level >= tab.requiredLevel;
  });

  const currentTab = availableTabs.find(tab => tab.id === activeTab) || availableTabs[0];
  const CurrentComponent = currentTab?.component;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading administration panel...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle size={48} className="text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Access Error</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Shield size={20} className="text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  Vaidya Administration
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Intelligent Care, Ancient Wisdom
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Welcome, {user?.firstName} {user?.lastName}
              </span>
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {user?.firstName?.[0]}{user?.lastName?.[0]}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Administration
              </h2>
              <nav className="space-y-2">
                {availableTabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                    >
                      <Icon size={18} />
                      <div className="flex-1">
                        <div className="font-medium">{tab.label}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {tab.description}
                        </div>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              {CurrentComponent && <CurrentComponent />}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
