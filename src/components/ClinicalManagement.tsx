import React, { useState, useEffect } from 'react';
import {
  FileText,
  Calendar,
  Clock,
  AlertCircle,
  Activity,
  Users,
  Plus,
  Edit,
  Eye,
  Stethoscope,
  Search,
  Filter,
  Loader,
  Bed
} from 'lucide-react';
import { clinicalAPI } from '../services/apiService';

interface MedicalRecord {
  _id: string;
  recordId: string;
  patient: {
    _id: string;
    firstName: string;
    lastName: string;
    patientId: string;
  };
  doctor: {
    _id: string;
    firstName: string;
    lastName: string;
    department: string;
  };
  appointment?: {
    _id: string;
    appointmentId: string;
    appointmentDate: string;
  };
  visitDate: string;
  visitType: string;
  chiefComplaint: string;
  presentIllnessHistory: string;
  pastMedicalHistory: string[];
  physicalExamination: {
    vitalSigns: {
      temperature: number;
      bloodPressure: string;
      heartRate: number;
      respiratoryRate: number;
      oxygenSaturation: number;
    };
    generalExamination: string;
    systemicExamination: string;
  };
  diagnosis: {
    primary: string;
    secondary: string[];
    differential: string[];
  };
  treatmentPlan: string;
  medications: Array<{
    name: string;
    dosage: string;
    frequency: string;
    duration: string;
  }>;
  followUpInstructions: string;
  notes: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export function ClinicalManagement() {
  const [medicalRecords, setMedicalRecords] = useState<MedicalRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRecord, setSelectedRecord] = useState<MedicalRecord | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const [activeTab, setActiveTab] = useState('beds');

  // Mock data for bed occupancy
  const bedOccupancy = [
    { ward: 'General Ward', occupied: 18, available: 7, total: 25 },
    { ward: 'ICU', occupied: 9, available: 1, total: 10 },
    { ward: 'Pediatrics', occupied: 12, available: 8, total: 20 },
  ];

  // Mock data for emergency/triage patients
  const emergencyPatients = [
    {
      id: 'e1',
      name: 'Alice Johnson',
      age: 45,
      condition: 'Chest Pain',
      triage: 'High',
      arrival: '10:05 AM',
      status: 'Waiting',
    },
    {
      id: 'e2',
      name: 'Bob Smith',
      age: 60,
      condition: 'Shortness of Breath',
      triage: 'High',
      arrival: '10:15 AM',
      status: 'In Treatment',
    },
    {
      id: 'e3',
      name: 'Carol Lee',
      age: 30,
      condition: 'Fracture',
      triage: 'Medium',
      arrival: '10:20 AM',
      status: 'Waiting',
    },
  ];

  // Mock data for surgery schedule
  const surgerySchedule = [
    {
      id: '1',
      patient: 'John Doe',
      procedure: 'Appendectomy',
      surgeon: 'Dr. Smith',
      theater: 'OT-1',
      time: '09:00 AM',
      duration: '2h',
      status: 'Scheduled',
    },
    {
      id: '2',
      patient: 'Jane Roe',
      procedure: 'Knee Replacement',
      surgeon: 'Dr. Adams',
      theater: 'OT-2',
      time: '11:30 AM',
      duration: '3h',
      status: 'In Progress',
    },
    {
      id: '3',
      patient: 'Sam Lee',
      procedure: 'Gallbladder Removal',
      surgeon: 'Dr. Brown',
      theater: 'OT-3',
      time: '02:00 PM',
      duration: '1.5h',
      status: 'Completed',
    },
  ];

  // Fetch medical records from API
  useEffect(() => {
    fetchMedicalRecords();
  }, [currentPage, searchTerm, statusFilter]);

  const fetchMedicalRecords = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        limit: 10
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }

      const response = await clinicalAPI.getMedicalRecords(params);

      if (response.success) {
        setMedicalRecords(response.data);
        setTotalPages(response.pagination?.pages || 1);
        setError(null);
      } else {
        setError('Failed to fetch medical records');
      }
    } catch (err) {
      console.error('Error fetching medical records:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch medical records');
    } finally {
      setLoading(false);
    }
  };

  const getTriageColor = (triage: string) => {
    switch (triage) {
      case 'High': return 'bg-red-100 text-red-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'In Progress': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'Scheduled': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300';
      case 'Completed': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'In Treatment': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300';
      case 'Waiting': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  const getBedOccupancyColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Clinical Management</h1>
        <div className="flex space-x-3">
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Plus size={20} />
            <span>Schedule Surgery</span>
          </button>
          <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Bed size={20} />
            <span>Assign Bed</span>
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('beds')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'beds'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Bed Management
            </button>
            <button
              onClick={() => setActiveTab('surgery')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'surgery'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Surgery Schedule
            </button>
            <button
              onClick={() => setActiveTab('emergency')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'emergency'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Emergency/Triage
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'beds' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Ward Occupancy</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Activity size={16} />
                  <span>Real-time updates</span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {bedOccupancy.map((ward) => {
                  const occupancyPercentage = (ward.occupied / ward.total) * 100;
                  return (
                    <div key={ward.ward} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">{ward.ward}</h3>
                        <Bed size={20} className="text-gray-500" />
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Occupied: {ward.occupied}</span>
                          <span>Available: {ward.available}</span>
                        </div>
                        
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${getBedOccupancyColor(occupancyPercentage)}`}
                            style={{ width: `${occupancyPercentage}%` }}
                          ></div>
                        </div>
                        
                        <div className="flex justify-between text-sm text-gray-500">
                          <span>Total: {ward.total}</span>
                          <span>{occupancyPercentage.toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'surgery' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Today's Surgery Schedule</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Clock size={16} />
                  <span>Updated 5 minutes ago</span>
                </div>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Patient</th>
                      <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Procedure</th>
                      <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Surgeon</th>
                      <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Theater</th>
                      <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Time</th>
                      <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Duration</th>
                      <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Status</th>
                      <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {surgerySchedule.map((surgery) => (
                      <tr key={surgery.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4 text-sm font-medium text-gray-900 dark:text-gray-100">{surgery.patient}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{surgery.procedure}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{surgery.surgeon}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{surgery.theater}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{surgery.time}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{surgery.duration}</td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(surgery.status)}`}>
                            {surgery.status}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            <button className="p-1 hover:bg-gray-100 rounded">
                              <Eye size={16} className="text-gray-500" />
                            </button>
                            <button className="p-1 hover:bg-gray-100 rounded">
                              <Edit size={16} className="text-gray-500" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'emergency' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Emergency Department</h2>
                <div className="flex items-center space-x-2 text-sm text-red-600">
                  <AlertCircle size={16} />
                  <span>3 High Priority Patients</span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {emergencyPatients.map((patient) => (
                  <div key={patient.id} className="bg-gray-50 rounded-lg p-4 border-l-4 border-red-500">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-gray-100">{patient.name}</h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTriageColor(patient.triage)}`}>
                        {patient.triage} Priority
                      </span>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Age:</span>
                        <span className="text-gray-900 dark:text-gray-100">{patient.age} years</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Condition:</span>
                        <span className="text-gray-900 dark:text-gray-100">{patient.condition}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Arrival:</span>
                        <span className="text-gray-900 dark:text-gray-100">{patient.arrival}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">Status:</span>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(patient.status)}`}>
                          {patient.status}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 mt-4">
                      <button className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                        Admit
                      </button>
                      <button className="flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                        Treat
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}