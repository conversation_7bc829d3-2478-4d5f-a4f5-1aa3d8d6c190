import React from 'react';
import { Home, ArrowLeft, Search, AlertTriangle } from 'lucide-react';

export function NotFound() {
  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center px-4">
      <div className="max-w-md w-full text-center">
        {/* Inception Logo */}
        <div className="mb-8">
          <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Inception</h1>
          <p className="text-sm text-gray-500">Intelligent Care, Ancient Wisdom</p>
        </div>

        {/* 404 Error */}
        <div className="mb-8">
          <h2 className="text-6xl font-bold text-blue-600 mb-4">404</h2>
          <h3 className="text-2xl font-semibold text-gray-900 mb-2">Page Not Found</h3>
          <p className="text-gray-600 mb-6">
            The page you're looking for doesn't exist or has been moved. 
            Please check the URL or navigate back to a valid page.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <button
            onClick={handleGoHome}
            className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
          >
            <Home size={20} />
            <span>Go to Dashboard</span>
          </button>

          <button
            onClick={handleGoBack}
            className="w-full bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center space-x-2"
          >
            <ArrowLeft size={20} />
            <span>Go Back</span>
          </button>
        </div>

        {/* Quick Links */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-4">Quick Links</h4>
          <div className="grid grid-cols-2 gap-3 text-sm">
            <a
              href="/patients"
              className="text-blue-600 hover:text-blue-800 hover:underline"
            >
              Patient Management
            </a>
            <a
              href="/appointments"
              className="text-blue-600 hover:text-blue-800 hover:underline"
            >
              Appointments
            </a>
            <a
              href="/triage"
              className="text-blue-600 hover:text-blue-800 hover:underline"
            >
              Triage
            </a>
            <a
              href="/consultation"
              className="text-blue-600 hover:text-blue-800 hover:underline"
            >
              Consultation
            </a>
          </div>
        </div>

        {/* Support Info */}
        <div className="mt-8 pt-8 border-t border-gray-200 text-xs text-gray-500">
          <p>If you believe this is an error, please contact support:</p>
          <p className="mt-1">
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
