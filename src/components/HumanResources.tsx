import React, { useState, useEffect } from 'react';
import {
  Users,
  UserPlus,
  Calendar,
  Clock,
  Award,
  Search,
  Filter,
  Mail,
  Phone,
  Loader,
  RefreshCw,
  AlertCircle,
  Edit,
  Eye
} from 'lucide-react';
import { hrAPI } from '../services/apiService';

interface StaffMember {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  department: string;
  position?: string;
  role: {
    _id: string;
    name: string;
    description?: string;
  };
  isActive: boolean;
  createdAt: string;
  lastLogin?: string;
  employeeId?: string;
  specialization?: string;
  schedule?: string;
  salary?: number;
}

interface HRStats {
  totalStaff: number;
  staffOnDutyToday: number;
  pendingEvaluations: number;
  staffByDepartment: Array<{
    _id: string;
    count: number;
  }>;
}



export function HumanResources() {
  const [activeTab, setActiveTab] = useState('staff');
  const [searchTerm, setSearchTerm] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [hrStats, setHrStats] = useState<HRStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Mock data for attendance, schedule, and payroll
  const [attendanceData] = useState([
    { id: 1, name: 'Dr. Sarah Johnson', present: 22, absent: 3, late: 1 },
    { id: 2, name: 'Nurse Mary Wilson', present: 24, absent: 1, late: 0 },
    { id: 3, name: 'Dr. Michael Brown', present: 20, absent: 5, late: 2 },
    { id: 4, name: 'Receptionist Lisa Davis', present: 23, absent: 2, late: 1 }
  ]);

  const [scheduleData] = useState([
    { id: 1, name: 'Dr. Sarah Johnson', shift: 'Morning', department: 'Cardiology', startTime: '08:00', endTime: '16:00' },
    { id: 2, name: 'Nurse Mary Wilson', shift: 'Night', department: 'Emergency', startTime: '20:00', endTime: '08:00' },
    { id: 3, name: 'Dr. Michael Brown', shift: 'Evening', department: 'Neurology', startTime: '14:00', endTime: '22:00' },
    { id: 4, name: 'Receptionist Lisa Davis', shift: 'Morning', department: 'Reception', startTime: '09:00', endTime: '17:00' }
  ]);

  const [payrollData] = useState([
    { id: 1, name: 'Dr. Sarah Johnson', baseSalary: 8000, overtime: 500, deductions: 200, netSalary: 8300, payDate: new Date('2025-01-01') },
    { id: 2, name: 'Nurse Mary Wilson', baseSalary: 4500, overtime: 300, deductions: 150, netSalary: 4650, payDate: new Date('2025-01-01') },
    { id: 3, name: 'Dr. Michael Brown', baseSalary: 7500, overtime: 400, deductions: 180, netSalary: 7720, payDate: new Date('2025-01-01') },
    { id: 4, name: 'Receptionist Lisa Davis', baseSalary: 3000, overtime: 100, deductions: 100, netSalary: 3000, payDate: new Date('2025-01-01') }
  ]);

  useEffect(() => {
    fetchStaffData();
    fetchHRStats();

    // Auto-refresh every 5 minutes
    const interval = setInterval(() => {
      fetchStaffData(true);
      fetchHRStats(true);
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchStaffData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const response = await hrAPI.getStaff({ limit: 100 });
      if (response.success) {
        setStaff(response.data);
        setError(null);
      } else {
        setError('Failed to fetch staff data');
      }
    } catch (err) {
      console.error('Error fetching staff:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch staff data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchHRStats = async (isRefresh = false) => {
    try {
      const response = await hrAPI.getStats();
      if (response.success) {
        setHrStats(response.data);
      }
    } catch (err) {
      console.error('Error fetching HR stats:', err);
    }
  };

  const handleRefresh = () => {
    fetchStaffData(true);
    fetchHRStats(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'On Leave': return 'bg-yellow-100 text-yellow-800';
      case 'Inactive': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredStaff = staff.filter(member => {
    const fullName = `${member.firstName} ${member.lastName}`;
    const matchesSearch = fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (member.employeeId && member.employeeId.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesDepartment = departmentFilter === 'all' || member.department.toLowerCase() === departmentFilter.toLowerCase();

    return matchesSearch && matchesDepartment;
  });

  const departments = ['all', ...new Set(staff.map(member => member.department))];

  const getStatusColorByActive = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const getStatusText = (isActive: boolean) => {
    return isActive ? 'Active' : 'Inactive';
  };

  const formatJoinDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Human Resources</h1>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <RefreshCw size={20} className={refreshing ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <UserPlus size={20} />
            <span>Add Staff</span>
          </button>
          <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Calendar size={20} />
            <span>Schedule</span>
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertCircle size={20} className="text-red-500" />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* HR Overview Cards */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-center py-8">
                <Loader className="animate-spin" size={24} />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Staff</p>
                <p className="text-2xl font-bold text-gray-900">
                  {hrStats?.totalStaff || staff.length}
                </p>
              </div>
              <Users size={24} className="text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Active Staff</p>
                <p className="text-2xl font-bold text-gray-900">
                  {staff.filter(member => member.isActive).length}
                </p>
              </div>
              <Users size={24} className="text-green-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">On Duty Today</p>
                <p className="text-2xl font-bold text-gray-900">
                  {hrStats?.staffOnDutyToday || 0}
                </p>
              </div>
              <Calendar size={24} className="text-yellow-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Departments</p>
                <p className="text-2xl font-bold text-gray-900">
                  {hrStats?.staffByDepartment?.length || new Set(staff.map(member => member.department)).size}
                </p>
              </div>
              <Award size={24} className="text-purple-500" />
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('staff')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'staff'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Staff Directory
            </button>
            <button
              onClick={() => setActiveTab('attendance')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'attendance'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Attendance
            </button>
            <button
              onClick={() => setActiveTab('schedule')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'schedule'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Schedule Management
            </button>
            <button
              onClick={() => setActiveTab('payroll')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'payroll'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Payroll
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'staff' && (
            <div className="space-y-6">
              {/* Search and Filter */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search staff by name, role, or department..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="flex items-center space-x-3">
                  <Filter size={20} className="text-gray-400" />
                  <select
                    value={departmentFilter}
                    onChange={(e) => setDepartmentFilter(e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {departments.map(dept => (
                      <option key={dept} value={dept}>
                        {dept === 'all' ? 'All Departments' : dept}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Staff Directory */}
              {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-6">
                      <div className="flex items-center justify-center py-8">
                        <Loader className="animate-spin" size={24} />
                      </div>
                    </div>
                  ))}
                </div>
              ) : filteredStaff.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="mx-auto mb-2 text-gray-400" size={32} />
                  <p className="text-gray-500">No staff members found</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredStaff.map((member) => (
                    <div key={member._id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                          <Users size={24} className="text-white" />
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColorByActive(member.isActive)}`}>{getStatusText(member.isActive)}</span>
                      </div>
                      <div className="space-y-2">
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">{member.firstName} {member.lastName}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{member.role.name}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{member.department}</p>
                        {member.position && (
                          <p className="text-sm text-gray-500 dark:text-gray-400">{member.position}</p>
                        )}
                        {member.employeeId && (
                          <p className="text-xs text-gray-400 dark:text-gray-500">ID: {member.employeeId}</p>
                        )}
                      </div>

                      <div className="pt-3 border-t border-gray-200">
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Mail size={14} />
                          <span className="truncate">{member.email}</span>
                        </div>
                        {member.phone && (
                          <div className="flex items-center space-x-2 text-sm text-gray-500 mt-1">
                            <Phone size={14} />
                            <span>{member.phone}</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-2 text-sm text-gray-400 mt-1">
                          <Calendar size={14} />
                          <span>Joined: {formatJoinDate(member.createdAt)}</span>
                        </div>
                      </div>

                      <div className="pt-3 flex space-x-2">
                        <button className="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-2 px-3 rounded flex items-center justify-center space-x-1">
                          <Eye size={12} />
                          <span>View</span>
                        </button>
                        <button className="flex-1 bg-gray-500 hover:bg-gray-600 text-white text-xs py-2 px-3 rounded flex items-center justify-center space-x-1">
                          <Edit size={12} />
                          <span>Edit</span>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'attendance' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Attendance Report</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Calendar size={16} />
                  <span>Current Month</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Staff Member</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Present Days</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Absent Days</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Late Arrivals</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Attendance %</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {attendanceData.map((record) => {
                      const totalDays = record.present + record.absent;
                      const attendancePercentage = (record.present / totalDays) * 100;
                      
                      return (
                        <tr key={record.id} className="hover:bg-gray-50">
                          <td className="py-4 px-6 text-sm font-medium text-gray-900">{record.name}</td>
                          <td className="py-4 px-6 text-sm text-gray-900">{record.present}</td>
                          <td className="py-4 px-6 text-sm text-gray-900">{record.absent}</td>
                          <td className="py-4 px-6 text-sm text-gray-900">{record.late}</td>
                          <td className="py-4 px-6">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              attendancePercentage >= 90 ? 'bg-green-100 text-green-800' :
                              attendancePercentage >= 80 ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {attendancePercentage.toFixed(1)}%
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'schedule' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Weekly Schedule</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Clock size={16} />
                  <span>Current Week</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Day</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Morning (6AM-2PM)</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Afternoon (2PM-10PM)</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Night (10PM-6AM)</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {scheduleData.map((schedule) => (
                      <tr key={schedule.day} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{schedule.day}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{schedule.morning}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{schedule.afternoon}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{schedule.night}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'payroll' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Payroll Management</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Calendar size={16} />
                  <span>Current Month</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Staff Member</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Role</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Department</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Annual Salary</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {staff.map((member) => (
                      <tr key={member.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{member.name}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{member.role}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{member.department}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">
                          ${member.salary.toLocaleString()}
                        </td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(member.status)}`}>
                            {member.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}