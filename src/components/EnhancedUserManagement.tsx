import React, { useState, useEffect } from 'react';
import {
  Users,
  UserPlus,
  Edit,
  Trash2,
  Key,
  Lock,
  Search,
  Filter,
  Download,
  Upload,
  CheckSquare,
  Square,
  MoreHorizontal,
  Eye,
  UserCheck,
  UserX,
  Mail,
  Phone,
  Calendar,
  Building,
  Shield,
  Activity
} from 'lucide-react';
import { adminAPI } from '../services/apiService';

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: {
    _id: string;
    name: string;
    level: number;
  };
  department: string;
  phone: string;
  employeeId: string;
  isActive: boolean;
  lastLogin: string;
  createdAt: string;
}

interface BulkOperation {
  operation: string;
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  requiresData?: boolean;
}

const BULK_OPERATIONS: BulkOperation[] = [
  { operation: 'activate', label: 'Activate Users', icon: UserCheck, color: 'green' },
  { operation: 'deactivate', label: 'Deactivate Users', icon: UserX, color: 'red' },
  { operation: 'assignRole', label: 'Assign Role', icon: Shield, color: 'blue', requiresData: true },
  { operation: 'updateDepartment', label: 'Update Department', icon: Building, color: 'purple', requiresData: true },
  { operation: 'delete', label: 'Delete Users', icon: Trash2, color: 'red' }
];

export function EnhancedUserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<any[]>([]);
  const [departments, setDepartments] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Selection and bulk operations
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showBulkMenu, setShowBulkMenu] = useState(false);
  const [bulkOperationData, setBulkOperationData] = useState<any>({});

  // Filtering and search
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // Modals
  const [showUserModal, setShowUserModal] = useState(false);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  useEffect(() => {
    fetchUsers();
    fetchRoles();
    fetchDepartments();
  }, [currentPage, searchTerm, roleFilter, departmentFilter, statusFilter, sortBy, sortOrder]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: searchTerm,
        role: roleFilter !== 'all' ? roleFilter : undefined,
        department: departmentFilter !== 'all' ? departmentFilter : undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        sortBy,
        sortOrder
      };

      const response = await adminAPI.getUsers(params);
      if (response.success) {
        setUsers(response.data);
        setTotalPages(response.pagination?.pages || 1);
        setError(null);
      } else {
        setError('Failed to fetch users');
      }
    } catch (err) {
      setError('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await adminAPI.getRoles();
      if (response.success) {
        setRoles(response.data);
      }
    } catch (err) {
      console.error('Error fetching roles:', err);
    }
  };

  const fetchDepartments = async () => {
    try {
      const response = await adminAPI.getDepartments();
      if (response.success) {
        setDepartments(response.data);
      }
    } catch (err) {
      console.error('Error fetching departments:', err);
    }
  };

  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(users.map(user => user._id));
    }
  };

  const handleBulkOperation = async (operation: string, data?: any) => {
    if (selectedUsers.length === 0) {
      setError('Please select users first');
      return;
    }

    setLoading(true);
    try {
      const response = await adminAPI.bulkUserOperations(operation, selectedUsers, data);
      if (response.success) {
        setSuccess(`Bulk ${operation} completed successfully`);
        setSelectedUsers([]);
        setShowBulkMenu(false);
        setBulkOperationData({});
        await fetchUsers();
      } else {
        setError(response.error || `Failed to perform bulk ${operation}`);
      }
    } catch (err) {
      setError(`Failed to perform bulk ${operation}`);
    } finally {
      setLoading(false);
    }
  };

  const handleExportUsers = async () => {
    try {
      // Implementation for user export
      setSuccess('User export started');
    } catch (err) {
      setError('Failed to export users');
    }
  };

  const handleImportUsers = async (file: File) => {
    try {
      // Implementation for user import
      setSuccess('User import started');
    } catch (err) {
      setError('Failed to import users');
    }
  };

  const filteredAndSortedUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' || 
      `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.employeeId.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  const getRoleColor = (roleName: string) => {
    switch (roleName) {
      case 'Super Admin': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300';
      case 'Administrator': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'Doctor': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'Nurse': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'Receptionist': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      default: return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300';
    }
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
      : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Enhanced User Management</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage users with advanced features and bulk operations
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleExportUsers}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Download size={16} />
            <span>Export</span>
          </button>
          <label className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors cursor-pointer">
            <Upload size={16} />
            <span>Import</span>
            <input
              type="file"
              accept=".csv,.xlsx"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleImportUsers(file);
              }}
              className="hidden"
            />
          </label>
          <button
            onClick={() => setShowUserModal(true)}
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <UserPlus size={16} />
            <span>Add User</span>
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <span className="text-green-700 dark:text-green-300">{success}</span>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <span className="text-red-700 dark:text-red-300">{error}</span>
        </div>
      )}

      {/* Filters and Search */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 p-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>

          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="all">All Roles</option>
            {roles.map(role => (
              <option key={role._id} value={role._id}>{role.name}</option>
            ))}
          </select>

          <select
            value={departmentFilter}
            onChange={(e) => setDepartmentFilter(e.target.value)}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="all">All Departments</option>
            {departments.map(dept => (
              <option key={dept} value={dept}>{dept}</option>
            ))}
          </select>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="all">All Status</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
          </select>

          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order as 'asc' | 'desc');
            }}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="name-asc">Name A-Z</option>
            <option value="name-desc">Name Z-A</option>
            <option value="email-asc">Email A-Z</option>
            <option value="createdAt-desc">Newest First</option>
            <option value="createdAt-asc">Oldest First</option>
            <option value="lastLogin-desc">Last Login</option>
          </select>
        </div>
      </div>

      {/* Bulk Operations */}
      {selectedUsers.length > 0 && (
        <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-purple-700 dark:text-purple-300">
              {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''} selected
            </span>
            <div className="flex space-x-2">
              {BULK_OPERATIONS.map(op => (
                <button
                  key={op.operation}
                  onClick={() => {
                    if (op.requiresData) {
                      setShowBulkMenu(true);
                    } else {
                      handleBulkOperation(op.operation);
                    }
                  }}
                  className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                    op.color === 'green' ? 'bg-green-500 hover:bg-green-600 text-white' :
                    op.color === 'red' ? 'bg-red-500 hover:bg-red-600 text-white' :
                    op.color === 'blue' ? 'bg-blue-500 hover:bg-blue-600 text-white' :
                    op.color === 'purple' ? 'bg-purple-500 hover:bg-purple-600 text-white' :
                    'bg-gray-500 hover:bg-gray-600 text-white'
                  }`}
                >
                  <op.icon size={14} className="inline mr-1" />
                  {op.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Users Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="text-left py-3 px-6">
                  <button
                    onClick={handleSelectAll}
                    className="flex items-center space-x-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    {selectedUsers.length === users.length && users.length > 0 ? (
                      <CheckSquare size={16} />
                    ) : (
                      <Square size={16} />
                    )}
                  </button>
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">User</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Role</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Department</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Status</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Last Login</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredAndSortedUsers.map(user => (
                <tr key={user._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="py-4 px-6">
                    <button
                      onClick={() => handleSelectUser(user._id)}
                      className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                    >
                      {selectedUsers.includes(user._id) ? (
                        <CheckSquare size={16} className="text-purple-600 dark:text-purple-400" />
                      ) : (
                        <Square size={16} />
                      )}
                    </button>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 dark:from-purple-600 dark:to-purple-700 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {user.firstName[0]}{user.lastName[0]}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-gray-100">
                          {user.firstName} {user.lastName}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center space-x-4">
                          <span className="flex items-center space-x-1">
                            <Mail size={12} />
                            <span>{user.email}</span>
                          </span>
                          {user.phone && (
                            <span className="flex items-center space-x-1">
                              <Phone size={12} />
                              <span>{user.phone}</span>
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-gray-400 dark:text-gray-500">
                          ID: {user.employeeId}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <span className={`px-3 py-1 text-xs font-medium rounded-full ${getRoleColor(user.role?.name || 'User')}`}>
                      {user.role?.name || 'No Role'}
                    </span>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-1 text-sm text-gray-900 dark:text-gray-100">
                      <Building size={14} className="text-gray-400 dark:text-gray-500" />
                      <span>{user.department || 'N/A'}</span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(user.isActive)}`}>
                      {user.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-1 text-sm text-gray-900 dark:text-gray-100">
                      <Calendar size={14} className="text-gray-400 dark:text-gray-500" />
                      <span>
                        {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                      </span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedUser(user);
                          setShowUserDetails(true);
                        }}
                        className="p-2 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors group"
                        title="View Details"
                      >
                        <Eye size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedUser(user);
                          setShowUserModal(true);
                        }}
                        className="p-2 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-colors group"
                        title="Edit User"
                      >
                        <Edit size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-purple-600 dark:group-hover:text-purple-400" />
                      </button>
                      <button
                        onClick={() => {
                          // Handle reset password
                        }}
                        className="p-2 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 rounded-lg transition-colors group"
                        title="Reset Password"
                      >
                        <Key size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-yellow-600 dark:group-hover:text-yellow-400" />
                      </button>
                      <button
                        onClick={() => {
                          // Handle toggle status
                        }}
                        className="p-2 hover:bg-orange-100 dark:hover:bg-orange-900/30 rounded-lg transition-colors group"
                        title="Toggle Status"
                      >
                        <Lock size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-orange-600 dark:group-hover:text-orange-400" />
                      </button>
                      <div className="relative">
                        <button
                          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                          title="More Actions"
                        >
                          <MoreHorizontal size={16} className="text-gray-500 dark:text-gray-400" />
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-gray-50 dark:bg-gray-700 px-6 py-3 border-t border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700 dark:text-gray-300">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => setItemsPerPage(parseInt(e.target.value))}
                  className="border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
                <span className="text-sm text-gray-700 dark:text-gray-300">per page</span>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                >
                  Previous
                </button>

                <span className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400">
                  Page {currentPage} of {totalPages}
                </span>

                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
