import React, { useState } from 'react';
import { adminAPI } from '../services/apiService';

export function AdminTest() {
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const testAPI = async (endpoint: string, apiCall: () => Promise<any>) => {
    setLoading(true);
    try {
      const result = await apiCall();
      setResults(prev => ({ ...prev, [endpoint]: { success: true, data: result } }));
    } catch (error: any) {
      setResults(prev => ({ ...prev, [endpoint]: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  const runTests = async () => {
    setResults({});
    
    // Test all admin API endpoints
    await testAPI('getAllUsers', () => adminAPI.getAllUsers());
    await testAPI('getAllRoles', () => adminAPI.getAllRoles());
    await testAPI('getAllPermissions', () => adminAPI.getAllPermissions());
    await testAPI('getAuditLogs', () => adminAPI.getAuditLogs());
    await testAPI('getSystemStats', () => adminAPI.getSystemStats());
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Admin API Test</h1>
      
      <button
        onClick={runTests}
        disabled={loading}
        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mb-6 disabled:opacity-50"
      >
        {loading ? 'Testing...' : 'Run API Tests'}
      </button>

      <div className="space-y-4">
        {Object.entries(results).map(([endpoint, result]: [string, any]) => (
          <div key={endpoint} className="border rounded p-4">
            <h3 className="font-semibold mb-2">{endpoint}</h3>
            <div className={`p-2 rounded ${result.success ? 'bg-green-100' : 'bg-red-100'}`}>
              {result.success ? (
                <div>
                  <p className="text-green-800 font-medium">✅ Success</p>
                  <pre className="text-sm mt-2 overflow-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </div>
              ) : (
                <div>
                  <p className="text-red-800 font-medium">❌ Error</p>
                  <p className="text-red-600 text-sm mt-1">{result.error}</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
