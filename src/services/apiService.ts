const API_BASE_URL = import.meta.env.VITE_REACT_APP_API_URL || 'http://localhost:3002/api';

// Generic API request function with automatic token refresh
const apiRequest = async (endpoint: string, options: RequestInit = {}, retryCount = 0): Promise<any> => {
  const token = localStorage.getItem('token');

  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

  // If token is expired and we haven't retried yet, try to refresh
  if (response.status === 401 && retryCount === 0 && token) {
    try {
      // Import authService dynamically to avoid circular dependency
      const { authService } = await import('./authService');
      const { token: newToken } = await authService.refreshToken();

      // Retry the request with the new token
      const newConfig: RequestInit = {
        ...config,
        headers: {
          ...config.headers,
          Authorization: `Bearer ${newToken}`,
        },
      };

      const retryResponse = await fetch(`${API_BASE_URL}${endpoint}`, newConfig);

      if (!retryResponse.ok) {
        const error = await retryResponse.json();
        throw new Error(error.error || 'API request failed');
      }

      return retryResponse.json();
    } catch (refreshError) {
      // If refresh fails, redirect to login
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      window.location.href = '/login';
      throw new Error('Session expired. Please log in again.');
    }
  }

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'API request failed');
  }

  return response.json();
};

// Patient API
export const patientAPI = {
  getAll: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/patients${queryString}`);
  },
  getById: (id: string) => apiRequest(`/patients/${id}`),
  getStats: () => apiRequest('/patients/stats'),
  create: (data: any) => apiRequest('/patients', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  update: (id: string, data: any) => apiRequest(`/patients/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  delete: (id: string) => apiRequest(`/patients/${id}`, {
    method: 'DELETE',
  }),
  search: (query: string) => apiRequest(`/patients/search?q=${query}`),
  getMedicalHistory: (id: string) => apiRequest(`/patients/${id}/medical-history`),
  getAppointments: (id: string) => apiRequest(`/patients/${id}/appointments`),
  admit: (id: string, data: any) => apiRequest(`/patients/${id}/admit`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  discharge: (id: string, data: any) => apiRequest(`/patients/${id}/discharge`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
};

// Patient Visit API
export const patientVisitAPI = {
  getAll: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/patient-visits${queryString}`);
  },
  getById: (id: string) => apiRequest(`/patient-visits/${id}`),
  create: (data: any) => apiRequest('/patient-visits', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  update: (id: string, data: any) => apiRequest(`/patient-visits/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  delete: (id: string) => apiRequest(`/patient-visits/${id}`, {
    method: 'DELETE',
  }),
  getPatientHistory: (patientId: string, params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/patient-visits/patient/${patientId}/history${queryString}`);
  },
  getStats: () => apiRequest('/patient-visits/stats'),
  updateStatus: (id: string, status: string) => apiRequest(`/patient-visits/${id}/status`, {
    method: 'PATCH',
    body: JSON.stringify({ status }),
  }),
};

// Treatment Plan API
export const treatmentPlanAPI = {
  getAll: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/treatment-plans${queryString}`);
  },
  getById: (id: string) => apiRequest(`/treatment-plans/${id}`),
  create: (data: any) => apiRequest('/treatment-plans', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  update: (id: string, data: any) => apiRequest(`/treatment-plans/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  delete: (id: string) => apiRequest(`/treatment-plans/${id}`, {
    method: 'DELETE',
  }),
  getPatientPlans: (patientId: string, params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/treatment-plans/patient/${patientId}${queryString}`);
  },
  getStats: () => apiRequest('/treatment-plans/stats'),
  updateStatus: (id: string, status: string) => apiRequest(`/treatment-plans/${id}/status`, {
    method: 'PATCH',
    body: JSON.stringify({ status }),
  }),
  addProgressNote: (id: string, data: any) => apiRequest(`/treatment-plans/${id}/progress-notes`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
};

// Appointment API
export const appointmentAPI = {
  getAll: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/appointments${queryString}`);
  },
  getById: (id: string) => apiRequest(`/appointments/${id}`),
  create: (data: any) => apiRequest('/appointments', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  update: (id: string, data: any) => apiRequest(`/appointments/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  delete: (id: string) => apiRequest(`/appointments/${id}`, {
    method: 'DELETE',
  }),
  getDoctorSchedule: (doctorId: string, date?: string) => {
    const params = date ? `?date=${date}` : '';
    return apiRequest(`/appointments/doctor/${doctorId}/schedule${params}`);
  },
  getAvailableSlots: (doctorId: string, date: string) => {
    return apiRequest(`/appointments/available-slots?doctorId=${doctorId}&date=${date}`);
  },
};

// Clinical API
export const clinicalAPI = {
  getMedicalRecords: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/clinical/medical-records${queryString}`);
  },
  getMedicalRecord: (id: string) => apiRequest(`/clinical/medical-records/${id}`),
  createMedicalRecord: (data: any) => apiRequest('/clinical/medical-records', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateMedicalRecord: (id: string, data: any) => apiRequest(`/clinical/medical-records/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  getPatientHistory: (patientId: string) => apiRequest(`/clinical/patients/${patientId}/history`),
};

// Laboratory API
export const laboratoryAPI = {
  getTests: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/laboratory/tests${queryString}`);
  },
  getTest: (id: string) => apiRequest(`/laboratory/tests/${id}`),
  createTest: (data: any) => apiRequest('/laboratory/tests', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateTest: (id: string, data: any) => apiRequest(`/laboratory/tests/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  getPatientTests: (patientId: string) => apiRequest(`/laboratory/patients/${patientId}/tests`),
  getStats: () => apiRequest('/laboratory/stats'),
};

// Pharmacy API
export const pharmacyAPI = {
  getInventory: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/pharmacy/inventory${queryString}`);
  },
  getInventoryItem: (id: string) => apiRequest(`/pharmacy/inventory/${id}`),
  createInventoryItem: (data: any) => apiRequest('/pharmacy/inventory', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateInventoryItem: (id: string, data: any) => apiRequest(`/pharmacy/inventory/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  getPrescriptions: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/pharmacy/prescriptions${queryString}`);
  },
  createPrescription: (data: any) => apiRequest('/pharmacy/prescriptions', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getStats: () => apiRequest('/pharmacy/stats'),

  // Billing methods
  getBills: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/pharmacy/bills${queryString}`);
  },
  createBill: (data: any) => apiRequest('/pharmacy/bills', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  downloadBillPDF: async (billId: string) => {
    const response = await fetch(`${API_BASE_URL}/pharmacy/bills/${billId}/pdf`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });
    if (!response.ok) {
      throw new Error('Failed to download bill PDF');
    }
    return response.blob();
  },

  // Analytics methods
  getAnalytics: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/pharmacy/analytics${queryString}`);
  },

  // Reports methods
  downloadInventoryReport: async () => {
    const response = await fetch(`${API_BASE_URL}/pharmacy/reports/inventory`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });
    if (!response.ok) {
      throw new Error('Failed to download inventory report');
    }
    return response.blob();
  },
};

// Medical API
export const medicalAPI = {
  getPrescriptions: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/medical/prescriptions${queryString}`);
  },
  createPrescription: (data: any) => apiRequest('/medical/prescriptions', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  downloadPrescriptionPDF: async (prescriptionId: string) => {
    const response = await fetch(`${API_BASE_URL}/medical/prescriptions/${prescriptionId}/pdf`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });
    if (!response.ok) {
      throw new Error('Failed to download prescription PDF');
    }
    return response.blob();
  },
};

// Financial API
export const financialAPI = {
  getBills: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/financial/bills${queryString}`);
  },
  getBill: (id: string) => apiRequest(`/financial/bills/${id}`),
  createBill: (data: any) => apiRequest('/financial/bills', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateBill: (id: string, data: any) => apiRequest(`/financial/bills/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  addPayment: (billId: string, data: any) => apiRequest(`/financial/bills/${billId}/payments`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getStats: () => apiRequest('/financial/stats'),
  getPaymentTrends: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/financial/payment-trends${queryString}`);
  },
  getOverdueBills: () => apiRequest('/financial/overdue-bills'),
  getRevenueAnalytics: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/financial/revenue-analytics${params}`);
  },
  getInsuranceClaims: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/financial/insurance-claims${queryString}`);
  },

  // Advanced Payment Processing
  processPayment: (data: any) => apiRequest('/financial/payments', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getPayments: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/financial/payments${queryString}`);
  },
  processRefund: (paymentId: string, data: any) => apiRequest(`/financial/payments/${paymentId}/refund`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  // Patient Payment History
  getPatientPaymentHistory: (patientId: string, params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/financial/patients/${patientId}/payments${queryString}`);
  },

  // Bill PDF Generation
  generateBillPDF: (billId: string) => {
    return fetch(`${API_BASE_URL}/financial/bills/${billId}/pdf`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });
  },

  // Payment Plans
  createPaymentPlan: (billId: string, data: any) => apiRequest(`/financial/bills/${billId}/payment-plan`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  // Advanced Financial Reports
  generateFinancialReport: (data: any) => {
    return fetch(`${API_BASE_URL}/financial/reports/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify(data),
    });
  },

  // Enhanced Analytics and Dashboard
  generateComprehensiveAnalytics: (data: any) => {
    return apiRequest('/financial/analytics/generate', {
      method: 'POST',
      body: JSON.stringify(data),
    }).catch(error => {
      console.warn('Analytics generation not available:', error);
      return { success: false, error: 'Analytics generation not available' };
    });
  },
  getFinancialDashboard: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/financial/dashboard${queryString}`).catch(error => {
      console.warn('Dashboard API not available:', error);
      return { success: false, error: 'Dashboard API not available' };
    });
  },

  // Universal Bill Creation
  createUniversalBill: (data: any) => {
    return apiRequest('/financial/bills/universal', {
      method: 'POST',
      body: JSON.stringify(data),
    }).catch(error => {
      console.warn('Universal bill creation not available, falling back to regular bill creation');
      // Fallback to regular bill creation
      return apiRequest('/financial/bills', {
        method: 'POST',
        body: JSON.stringify(data),
      });
    });
  },
};

// HR API
export const hrAPI = {
  getStaff: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/hr/staff${queryString}`);
  },
  getSchedules: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/hr/schedules${queryString}`);
  },
  createSchedule: (data: any) => apiRequest('/hr/schedules', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getPerformance: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/hr/performance${queryString}`);
  },
  createPerformance: (data: any) => apiRequest('/hr/performance', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getStats: () => apiRequest('/hr/stats'),
};

// Facility API
export const facilityAPI = {
  getRooms: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/facility/rooms${queryString}`);
  },
  createRoom: (data: any) => apiRequest('/facility/rooms', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getEquipment: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/facility/equipment${queryString}`);
  },
  createEquipment: (data: any) => apiRequest('/facility/equipment', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getMaintenanceRequests: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/facility/maintenance${queryString}`);
  },
  createMaintenanceRequest: (data: any) => apiRequest('/facility/maintenance', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getStats: () => apiRequest('/facility/stats'),
};

// Reports API
export const reportsAPI = {
  getDashboard: () => apiRequest('/reports/dashboard'),
  getRecentActivity: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/reports/recent-activity${queryString}`);
  },
  getCategories: () => apiRequest('/reports/categories'),
  getPatientAnalytics: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/patients${params}`);
  },
  getAppointmentAnalytics: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/appointments${params}`);
  },
  getFinancialAnalytics: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/financial${params}`);
  },
  getLaboratoryAnalytics: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/laboratory${params}`);
  },
  getOperationalReports: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/operational${params}`);
  },
  getClinicalReports: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/clinical${params}`);
  },
  getQualityReports: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/quality${params}`);
  },
  generateCustomReport: (data: any) => apiRequest('/reports/custom', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  exportReport: (reportType: string, format: string = 'pdf', data?: any) => {
    const params = new URLSearchParams({ format });
    if (data) {
      Object.keys(data).forEach(key => params.append(key, data[key]));
    }
    return apiRequest(`/reports/${reportType}/export?${params.toString()}`, {
      method: 'GET',
    });
  },
};

// Notifications API
export const notificationAPI = {
  getAll: (params?: any) => {
    const queryParams = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/notifications${queryParams}`);
  },
  getUnreadCount: () => apiRequest('/notifications/unread-count'),
  markAsRead: (id: string) => apiRequest(`/notifications/${id}/read`, {
    method: 'PUT',
  }),
  markAllAsRead: () => apiRequest('/notifications/mark-all-read', {
    method: 'PUT',
  }),
  create: (data: any) => apiRequest('/notifications', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  delete: (id: string) => apiRequest(`/notifications/${id}`, {
    method: 'DELETE',
  }),
  getPreferences: () => apiRequest('/notifications/preferences'),
  updatePreferences: (preferences: any) => apiRequest('/notifications/preferences', {
    method: 'PUT',
    body: JSON.stringify({ preferences }),
  }),
};

// User Profile API
export const userAPI = {
  getProfile: () => apiRequest('/auth/profile'),
  updateProfile: (data: any) => apiRequest('/auth/profile', {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  changePassword: (data: { currentPassword: string; newPassword: string }) => apiRequest('/auth/change-password', {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  uploadAvatar: (file: File) => {
    const formData = new FormData();
    formData.append('avatar', file);
    return apiRequest('/auth/upload-avatar', {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set content-type for FormData
    });
  },
};

// Admin API
export const adminAPI = {
  // User Management
  getUsers: (params?: any) => {
    const queryParams = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/admin/users${queryParams}`);
  },
  createUser: (data: any) => apiRequest('/admin/users', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateUser: (id: string, data: any) => apiRequest(`/admin/users/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  deleteUser: (id: string) => apiRequest(`/admin/users/${id}`, {
    method: 'DELETE',
  }),
  resetUserPassword: (id: string, newPassword: string) => apiRequest(`/admin/users/${id}/reset-password`, {
    method: 'PUT',
    body: JSON.stringify({ newPassword }),
  }),
  toggleUserStatus: (id: string) => apiRequest(`/admin/users/${id}/toggle-status`, {
    method: 'PUT',
  }),
  getUserStats: () => apiRequest('/admin/users/stats'),

  // Role Management
  getRoles: () => apiRequest('/admin/roles'),
  createRole: (data: any) => apiRequest('/admin/roles', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateRole: (id: string, data: any) => apiRequest(`/admin/roles/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  deleteRole: (id: string) => apiRequest(`/admin/roles/${id}`, {
    method: 'DELETE',
  }),

  // Audit Logs
  getAuditLogs: (params?: any) => {
    const queryParams = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/admin/audit-logs${queryParams}`);
  },
  createAuditLog: (data: any) => apiRequest('/admin/audit-logs', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  // System Statistics
  getSystemStats: () => apiRequest('/admin/system-stats'),

  // Get departments
  getDepartments: () => apiRequest('/admin/departments'),

  // Enhanced permission system
  initializeSystem: () => apiRequest('/admin/initialize-system', {
    method: 'POST',
  }),
  getPermissionCategories: () => apiRequest('/admin/permissions/categories'),
  getAllPermissions: () => apiRequest('/admin/permissions/all'),
  assignPermissionsToRole: (roleId: string, permissionIds: string[]) => {
    if (!roleId || !Array.isArray(permissionIds)) {
      return Promise.reject(new Error('Role ID and permissions array are required'));
    }
    return apiRequest(`/admin/roles/${roleId}/permissions`, {
      method: 'PUT',
      body: JSON.stringify({ permissionIds }),
    });
  },
  assignRoleToUser: (userId: string, roleId: string) => {
    if (!userId || !roleId) {
      return Promise.reject(new Error('User ID and Role ID are required'));
    }
    return apiRequest(`/admin/users/${userId}/role`, {
      method: 'PUT',
      body: JSON.stringify({ roleId }),
    });
  },

  // Additional admin API methods
  createUser: (userData: any) => apiRequest('/admin/users', {
    method: 'POST',
    body: JSON.stringify(userData),
  }),

  updateUser: (userId: string, userData: any) => apiRequest(`/admin/users/${userId}`, {
    method: 'PUT',
    body: JSON.stringify(userData),
  }),

  deleteUser: (userId: string) => apiRequest(`/admin/users/${userId}`, {
    method: 'DELETE',
  }),

  createRole: (roleData: any) => apiRequest('/admin/roles', {
    method: 'POST',
    body: JSON.stringify(roleData),
  }),

  updateRole: (roleId: string, roleData: any) => apiRequest(`/admin/roles/${roleId}`, {
    method: 'PUT',
    body: JSON.stringify(roleData),
  }),

  deleteRole: (roleId: string) => apiRequest(`/admin/roles/${roleId}`, {
    method: 'DELETE',
  }),

  createPermission: (permissionData: any) => apiRequest('/admin/permissions', {
    method: 'POST',
    body: JSON.stringify(permissionData),
  }),

  updatePermission: (permissionId: string, permissionData: any) => apiRequest(`/admin/permissions/${permissionId}`, {
    method: 'PUT',
    body: JSON.stringify(permissionData),
  }),

  deletePermission: (permissionId: string) => apiRequest(`/admin/permissions/${permissionId}`, {
    method: 'DELETE',
  }),

  getAuditLogs: (params?: any) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/admin/audit-logs${queryString}`);
  },

  exportAuditLogs: (params?: any) => apiRequest('/admin/audit-logs/export', {
    method: 'POST',
    body: JSON.stringify(params || {}),
  }),

  getSystemSettings: () => apiRequest('/admin/system/settings'),

  updateSystemSettings: (settings: any) => apiRequest('/admin/system/settings', {
    method: 'PUT',
    body: JSON.stringify({ settings }),
  }),
  bulkUserOperations: (operation: string, userIds: string[], data?: any) => apiRequest('/admin/users/bulk', {
    method: 'POST',
    body: JSON.stringify({ operation, userIds, data }),
  }),
  getUserActivity: (userId: string, days?: number) => {
    const params = days ? `?days=${days}` : '';
    return apiRequest(`/admin/users/${userId}/activity${params}`);
  },

  // Advanced audit system
  getAuditStats: (params?: any) => {
    const queryParams = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/admin/audit-stats${queryParams}`);
  },
  exportAuditLogs: (params?: any) => {
    const queryParams = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/admin/audit-logs/export${queryParams}`);
  },
};

export default {
  patient: patientAPI,
  appointment: appointmentAPI,
  clinical: clinicalAPI,
  laboratory: laboratoryAPI,
  pharmacy: pharmacyAPI,
  medical: medicalAPI,
  financial: financialAPI,
  hr: hrAPI,
  facility: facilityAPI,
  reports: reportsAPI,
};
