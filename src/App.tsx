import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import { LoginForm } from './components/LoginForm';
import { Signup } from './components/Signup';
import { Dashboard } from './components/Dashboard';
import { PatientManagement } from './components/PatientManagement';
import { PatientOverview } from './components/patient/PatientOverview';
import { VisitManagement } from './components/patient/VisitManagement';
import { MedicalHistory } from './components/patient/MedicalHistory';
import { TreatmentPlans } from './components/patient/TreatmentPlans';
import { PatientBilling } from './components/patient/PatientBilling';
import { PatientDocuments } from './components/patient/PatientDocuments';
import { PatientAnalytics } from './components/patient/PatientAnalytics';
import { PatientCommunication } from './components/patient/PatientCommunication';
import { ReportGeneration } from './components/patient/ReportGeneration';
import { PatientJourney } from './components/patient/PatientJourney';
import { PatientRegistration } from './components/patient/PatientRegistration';
import { AppointmentScheduling } from './components/appointments/AppointmentScheduling';
import { TriageManagement } from './components/triage/TriageManagement';
import { ConsultationWorkflow } from './components/consultation/ConsultationWorkflow';
import { NotFound } from './components/ErrorPages/NotFound';
import { AppointmentManagement } from './components/AppointmentManagement';
import { ClinicalManagement } from './components/ClinicalManagement';
import { Laboratory } from './components/Laboratory';
import { Pharmacy } from './components/Pharmacy';
import { FinancialManagement } from './components/FinancialManagement';
import { HumanResources } from './components/HumanResources';
import { FacilityManagement } from './components/FacilityManagement';
import { Administration } from './components/Administration';
import { AdminTest } from './components/AdminTest';
import { Reports } from './components/Reports';
import { UserManagement } from './components/UserManagement';
import { Notifications } from './components/Notifications';
import { UserProfile } from './components/UserProfile';
import { Settings } from './components/Settings';
import { RoleBasedSidebar } from './components/RoleBasedSidebar';
import { Header } from './components/Header';
import { ProtectedRoute } from './components/ProtectedRoute';
import { LoadingScreen } from './components/LoadingScreen';
import { ThemeProvider } from './context/ThemeContext';

export type ActiveModule = 'dashboard' | 'patients' | 'clinical' | 'laboratory' | 'pharmacy' | 'financial' | 'hr' | 'facility' | 'admin' | 'reports' | 'users';

// Layout component for authenticated routes
function DashboardLayout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-gray-900 dark:to-gray-800 flex">
      <RoleBasedSidebar
        isOpen={sidebarOpen}
        onToggle={() => setSidebarOpen(!sidebarOpen)}
      />

      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
        <Header onMenuToggle={() => setSidebarOpen(!sidebarOpen)} />
        <main className="p-6 min-h-screen">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}

function AppContent() {
  const { loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route path="/login" element={<LoginForm />} />
      <Route path="/signup" element={<Signup />} />

      {/* Protected routes */}
      <Route path="/dashboard" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Dashboard />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PatientManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/overview" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PatientOverview />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/visits" element={
        <ProtectedRoute>
          <DashboardLayout>
            <VisitManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/medical-history" element={
        <ProtectedRoute>
          <DashboardLayout>
            <MedicalHistory />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/treatment-plans" element={
        <ProtectedRoute>
          <DashboardLayout>
            <TreatmentPlans />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/billing" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PatientBilling />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/documents" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PatientDocuments />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/analytics" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PatientAnalytics />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/communication" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PatientCommunication />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/reports" element={
        <ProtectedRoute>
          <DashboardLayout>
            <ReportGeneration />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/journey" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PatientJourney />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/registration" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PatientRegistration />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/appointments" element={
        <ProtectedRoute>
          <DashboardLayout>
            <AppointmentScheduling />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/triage" element={
        <ProtectedRoute>
          <DashboardLayout>
            <TriageManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/consultation" element={
        <ProtectedRoute>
          <DashboardLayout>
            <ConsultationWorkflow />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* 404 Not Found Route */}
      <Route path="*" element={<NotFound />} />

      <Route path="/appointments" element={
        <ProtectedRoute>
          <DashboardLayout>
            <AppointmentManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/clinical" element={
        <ProtectedRoute>
          <DashboardLayout>
            <ClinicalManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/laboratory" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Laboratory />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/pharmacy" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Pharmacy />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/financial" element={
        <ProtectedRoute>
          <DashboardLayout>
            <FinancialManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/hr" element={
        <ProtectedRoute>
          <DashboardLayout>
            <HumanResources />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/facility" element={
        <ProtectedRoute>
          <DashboardLayout>
            <FacilityManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/admin" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Administration />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/admin-test" element={
        <ProtectedRoute>
          <DashboardLayout>
            <AdminTest />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/reports" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Reports />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/users" element={
        <ProtectedRoute>
          <DashboardLayout>
            <UserManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/notifications" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Notifications />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/profile" element={
        <ProtectedRoute>
          <DashboardLayout>
            <UserProfile />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/settings" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Settings />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Default redirect */}
      <Route path="/" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
}

function App() {
  return (
    <Router>
      <ThemeProvider>
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      </ThemeProvider>
    </Router>
  );
}

export default App;