# Inception HMS - Administration System Documentation

## Overview

The Inception Hospital Management System (HMS) Administration module provides comprehensive system management capabilities with role-based access control, user management, audit logging, compliance monitoring, and system configuration.

**Tagline**: *Intelligent Care, Ancient Wisdom*

## Features

### 1. User Management
- **Enhanced User Management**: Create, edit, delete, and manage user accounts
- **Bulk Operations**: Import/export users, bulk role assignments
- **User Profiles**: Comprehensive user profiles with department, availability, and contact information
- **Status Management**: Active/inactive user status control
- **Search & Filtering**: Advanced search and filtering capabilities

### 2. Role Management
- **Hierarchical Roles**: Role levels from 1-10 with inheritance
- **Permission Assignment**: Granular permission management
- **System Roles**: Protected system roles (Administrator, Doctor, Nurse, Receptionist)
- **Custom Roles**: Create custom roles with specific permissions
- **Role Templates**: Pre-defined role templates for quick setup

### 3. Permission System
- **Granular Permissions**: Module-based permissions (view, create, edit, delete, export, approve, assign)
- **Permission Categories**: Organized by functional areas
- **Resource-Specific**: Permissions can be resource-specific or wildcard
- **Inheritance**: Role-based permission inheritance
- **Override Capability**: User-specific permission overrides

### 4. Audit System
- **Comprehensive Logging**: All system actions are logged
- **Real-time Monitoring**: Live audit trail with filtering
- **Export Capabilities**: Export audit logs in multiple formats
- **Search & Filter**: Advanced search across audit logs
- **Retention Policies**: Configurable log retention

### 5. System Settings
- **General Settings**: System name, tagline, timezone, language
- **Security Policies**: Password requirements, session timeout, login attempts
- **Email Configuration**: SMTP settings for notifications
- **Notification Settings**: Email/SMS notification preferences
- **Backup Configuration**: Automated backup settings

### 6. Backup & Maintenance
- **Automated Backups**: Scheduled backup creation
- **Manual Backups**: On-demand backup creation
- **Backup Management**: View, download, restore, delete backups
- **Maintenance Tasks**: Database optimization, log cleanup, security scans
- **System Health**: Monitor CPU, memory, storage, database health

### 7. Compliance Monitoring
- **Regulatory Standards**: HIPAA, GDPR, SOC 2, HL7 FHIR compliance
- **Compliance Scoring**: Overall compliance percentage tracking
- **Requirements Tracking**: Individual requirement status monitoring
- **Security Metrics**: Failed logins, password compliance, encryption status
- **Audit Reports**: Compliance audit reports and documentation

## Architecture

### Backend Components

#### Models
- **User**: User account information with role and permissions
- **Role**: Role definitions with permission assignments
- **Permission**: Granular permission definitions
- **AuditLog**: System activity logging

#### Controllers
- **adminController.js**: Main administration logic
- **authController.js**: Authentication and authorization
- **userController.js**: User-specific operations

#### Middleware
- **authMiddleware.js**: Authentication verification
- **permissionMiddleware.js**: Permission checking
- **auditMiddleware.js**: Audit logging

### Frontend Components

#### Main Components
- **Administration.tsx**: Main administration interface
- **EnhancedUserManagement.tsx**: User management interface
- **SuperAdminPanel.tsx**: Super admin controls
- **PermissionManagement.tsx**: Permission assignment interface
- **AdvancedAuditSystem.tsx**: Audit log viewer
- **SystemSettings.tsx**: System configuration
- **BackupMaintenance.tsx**: Backup and maintenance tools
- **ComplianceMonitoring.tsx**: Compliance dashboard

#### Supporting Components
- **NotificationSystem.tsx**: User notifications
- **LoadingSpinner.tsx**: Loading states
- **Modal components**: Various modal dialogs

## Permission Categories

### Dashboard
- View dashboard and statistics

### User Management
- View, create, edit, delete user accounts
- Assign roles to users

### Role Management
- View, create, edit, delete roles
- Assign permissions to roles

### Patient Management
- View, create, edit, delete patient records
- Export patient data

### Appointment Management
- View, create, edit, delete appointments
- Manage schedules

### Clinical Management
- View, create, edit medical records
- Manage doctor schedules

### Laboratory Management
- View, create, edit lab tests
- Approve lab results

### Pharmacy Management
- View, create, edit prescriptions
- Manage inventory

### Financial Management
- View, create, edit bills
- Process payments
- Export financial reports

### Human Resources
- View, create, edit staff records
- Manage schedules

### Facility Management
- View, create, edit facility information
- Manage equipment

### System Administration
- View, edit system settings
- Access audit logs

### Reports
- View, create, export reports
- Access analytics

## API Endpoints

### User Management
```
GET    /api/admin/users              - Get all users
POST   /api/admin/users              - Create user
PUT    /api/admin/users/:id          - Update user
DELETE /api/admin/users/:id          - Delete user
PUT    /api/admin/users/:id/role     - Assign role to user
```

### Role Management
```
GET    /api/admin/roles              - Get all roles
POST   /api/admin/roles              - Create role
PUT    /api/admin/roles/:id          - Update role
DELETE /api/admin/roles/:id          - Delete role
PUT    /api/admin/roles/:id/permissions - Assign permissions to role
```

### Permission Management
```
GET    /api/admin/permissions        - Get all permissions
POST   /api/admin/permissions        - Create permission
PUT    /api/admin/permissions/:id    - Update permission
DELETE /api/admin/permissions/:id    - Delete permission
```

### Audit System
```
GET    /api/admin/audit-logs         - Get audit logs
POST   /api/admin/audit-logs/export  - Export audit logs
```

### System Management
```
GET    /api/admin/system/stats       - Get system statistics
POST   /api/admin/system/initialize  - Initialize system
GET    /api/admin/system/health      - Get system health
```

## Security Features

### Authentication
- JWT-based authentication
- Session management
- Password hashing with bcrypt
- Account lockout after failed attempts

### Authorization
- Role-based access control (RBAC)
- Permission-based authorization
- Hierarchical role system
- Resource-specific permissions

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection
- Data encryption at rest and in transit

### Audit & Compliance
- Comprehensive audit logging
- HIPAA compliance features
- GDPR compliance tools
- Data retention policies
- Access monitoring

## Installation & Setup

### Prerequisites
- Node.js 16+
- MongoDB 4.4+
- React 18+
- TypeScript support

### Backend Setup
```bash
cd server
npm install
npm run seed:permissions  # Initialize permissions
npm run dev
```

### Frontend Setup
```bash
cd client
npm install
npm start
```

### Database Seeding
```bash
# Seed permissions and roles
node server/utils/seedPermissions.js

# Seed sample data
node server/scripts/seedDatabase.js
```

## Configuration

### Environment Variables
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/Inception_hms
JWT_SECRET=your_jwt_secret
JWT_EXPIRE=30d
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

### Default Roles
- **Administrator** (Level 10): Full system access
- **Doctor** (Level 7): Patient care and medical records
- **Nurse** (Level 5): Patient care support
- **Receptionist** (Level 3): Front desk operations

## Usage Guidelines

### Super Admin Functions
- System initialization
- Role and permission management
- User account management
- System configuration
- Audit log access

### Regular Admin Functions
- User management within role hierarchy
- Basic system monitoring
- Report generation
- Backup management

### Security Best Practices
- Regular password updates
- Role-based access principle
- Audit log monitoring
- Regular system backups
- Compliance monitoring

## Troubleshooting

### Common Issues
1. **Permission Denied**: Check user role and permissions
2. **Role Assignment Failed**: Verify role hierarchy
3. **Audit Log Missing**: Check audit middleware configuration
4. **Backup Failed**: Verify storage permissions and space

### Error Codes
- **401**: Unauthorized - Invalid or missing authentication
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource doesn't exist
- **500**: Server Error - Internal system error

## Support & Maintenance

### Regular Maintenance
- Database optimization
- Log cleanup
- Security updates
- Backup verification
- Performance monitoring

### Monitoring
- System health checks
- User activity monitoring
- Error rate tracking
- Performance metrics
- Compliance status

---

**Version**: 2.0.0  
**Last Updated**: January 2024  
**Maintained by**: Inception HMS Development Team
