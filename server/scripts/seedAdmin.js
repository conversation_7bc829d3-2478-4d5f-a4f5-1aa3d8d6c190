import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import Role from '../models/Role.js';
import Permission from '../models/Permission.js';
import User from '../models/User.js';

dotenv.config();

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/vaidya_hms');
    console.log('✅ MongoDB connected for seeding');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const seedPermissions = async () => {
  console.log('🌱 Seeding permissions...');
  
  const permissions = [
    // User Management
    { module: 'users', action: 'view', resource: '*', description: 'View all users', category: 'User Management' },
    { module: 'users', action: 'create', resource: '*', description: 'Create new users', category: 'User Management' },
    { module: 'users', action: 'edit', resource: '*', description: 'Edit user details', category: 'User Management' },
    { module: 'users', action: 'delete', resource: '*', description: 'Delete users', category: 'User Management' },
    
    // Role Management
    { module: 'roles', action: 'view', resource: '*', description: 'View all roles', category: 'Role Management' },
    { module: 'roles', action: 'create', resource: '*', description: 'Create new roles', category: 'Role Management' },
    { module: 'roles', action: 'edit', resource: '*', description: 'Edit role details', category: 'Role Management' },
    { module: 'roles', action: 'delete', resource: '*', description: 'Delete roles', category: 'Role Management' },
    
    // Patient Management
    { module: 'patients', action: 'view', resource: '*', description: 'View all patients', category: 'Patient Management' },
    { module: 'patients', action: 'create', resource: '*', description: 'Create new patients', category: 'Patient Management' },
    { module: 'patients', action: 'edit', resource: '*', description: 'Edit patient details', category: 'Patient Management' },
    { module: 'patients', action: 'delete', resource: '*', description: 'Delete patients', category: 'Patient Management' },
    
    // Appointment Management
    { module: 'appointments', action: 'view', resource: '*', description: 'View all appointments', category: 'Appointment Management' },
    { module: 'appointments', action: 'create', resource: '*', description: 'Create new appointments', category: 'Appointment Management' },
    { module: 'appointments', action: 'edit', resource: '*', description: 'Edit appointments', category: 'Appointment Management' },
    { module: 'appointments', action: 'delete', resource: '*', description: 'Delete appointments', category: 'Appointment Management' },
    
    // System Administration
    { module: 'admin', action: 'view', resource: '*', description: 'View admin panel', category: 'System Administration' },
    { module: 'admin', action: 'edit', resource: '*', description: 'Edit system settings', category: 'System Administration' },
    { module: 'system', action: 'view', resource: '*', description: 'View system information', category: 'System Administration' },
    { module: 'system', action: 'edit', resource: '*', description: 'Edit system configuration', category: 'System Administration' },
    
    // Audit & Reports
    { module: 'audit', action: 'view', resource: '*', description: 'View audit logs', category: 'Audit & Reports' },
    { module: 'audit', action: 'export', resource: '*', description: 'Export audit logs', category: 'Audit & Reports' },
    { module: 'reports', action: 'view', resource: '*', description: 'View reports', category: 'Reports' },
    { module: 'reports', action: 'generate', resource: '*', description: 'Generate reports', category: 'Reports' }
  ];

  for (const permData of permissions) {
    const existing = await Permission.findOne({ 
      module: permData.module, 
      action: permData.action, 
      resource: permData.resource 
    });
    
    if (!existing) {
      await Permission.create({ ...permData, isSystemPermission: true });
      console.log(`✅ Created permission: ${permData.module}:${permData.action}:${permData.resource}`);
    }
  }
};

const seedRoles = async () => {
  console.log('🌱 Seeding roles...');
  
  // Get all permissions
  const allPermissions = await Permission.find({});
  const adminPermissions = allPermissions.map(p => p._id);
  const doctorPermissions = allPermissions.filter(p => 
    ['patients', 'appointments', 'reports'].includes(p.module)
  ).map(p => p._id);
  const nursePermissions = allPermissions.filter(p => 
    ['patients', 'appointments'].includes(p.module) && p.action !== 'delete'
  ).map(p => p._id);
  const receptionistPermissions = allPermissions.filter(p => 
    ['appointments'].includes(p.module)
  ).map(p => p._id);

  const roles = [
    {
      name: 'Administrator',
      description: 'Full system access with all administrative privileges',
      level: 10,
      defaultPermissions: adminPermissions,
      isSystemRole: true
    },
    {
      name: 'Doctor',
      description: 'Medical staff with patient care and medical record access',
      level: 7,
      defaultPermissions: doctorPermissions,
      isSystemRole: true
    },
    {
      name: 'Nurse',
      description: 'Nursing staff with patient care and medication management',
      level: 5,
      defaultPermissions: nursePermissions,
      isSystemRole: true
    },
    {
      name: 'Receptionist',
      description: 'Front desk staff with appointment and basic patient management',
      level: 3,
      defaultPermissions: receptionistPermissions,
      isSystemRole: true
    }
  ];

  for (const roleData of roles) {
    const existing = await Role.findOne({ name: roleData.name });
    if (!existing) {
      await Role.create(roleData);
      console.log(`✅ Created role: ${roleData.name} (Level ${roleData.level})`);
    }
  }
};

const seedAdminUser = async () => {
  console.log('🌱 Seeding admin user...');
  
  const adminRole = await Role.findOne({ name: 'Administrator' });
  if (!adminRole) {
    console.error('❌ Administrator role not found');
    return;
  }

  const existingAdmin = await User.findOne({ email: '<EMAIL>' });
  if (!existingAdmin) {
    const hashedPassword = await bcrypt.hash('admin123', 12);
    
    await User.create({
      firstName: 'System',
      lastName: 'Administrator',
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      role: adminRole._id,
      permissions: adminRole.defaultPermissions,
      department: 'Administration',
      employeeId: 'EMP001',
      position: 'System Administrator',
      isActive: true
    });
    
    console.log('✅ Created admin user: <EMAIL> / admin123');
  }
};

const seedDoctorUser = async () => {
  console.log('🌱 Seeding doctor user...');
  
  const doctorRole = await Role.findOne({ name: 'Doctor' });
  if (!doctorRole) {
    console.error('❌ Doctor role not found');
    return;
  }

  const existingDoctor = await User.findOne({ email: '<EMAIL>' });
  if (!existingDoctor) {
    const hashedPassword = await bcrypt.hash('doctor123', 12);
    
    await User.create({
      firstName: 'Dr. John',
      lastName: 'Smith',
      email: '<EMAIL>',
      username: 'doctor',
      password: hashedPassword,
      role: doctorRole._id,
      permissions: doctorRole.defaultPermissions,
      department: 'General Medicine',
      employeeId: 'EMP002',
      position: 'Senior Doctor',
      specialization: 'General Medicine',
      licenseNumber: 'MD12345',
      isActive: true
    });
    
    console.log('✅ Created doctor user: <EMAIL> / doctor123');
  }
};

const main = async () => {
  try {
    await connectDB();
    
    await seedPermissions();
    await seedRoles();
    await seedAdminUser();
    await seedDoctorUser();
    
    console.log('🎉 Seeding completed successfully!');
    console.log('');
    console.log('Test accounts created:');
    console.log('👤 Admin: <EMAIL> / admin123');
    console.log('👨‍⚕️ Doctor: <EMAIL> / doctor123');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  }
};

main();
