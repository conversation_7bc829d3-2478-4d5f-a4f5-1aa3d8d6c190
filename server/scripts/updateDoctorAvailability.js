// Usage: node server/scripts/updateDoctorAvailability.js
import mongoose from 'mongoose';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import User from '../models/User.js';
import Role from '../models/Role.js';
import Permission from '../models/Permission.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.resolve(__dirname, '../../.env') });

const MONGO_URI = process.env.MONGODB_URI;

const standardAvailability = [];
// Sunday (0): off
// Monday-Saturday (1-6): 9-13, 14-18
for (let day = 0; day < 7; day++) {
  if (day === 0) continue; // Sunday off
  standardAvailability.push(
    { dayOfWeek: day, startTime: '09:00', endTime: '13:00', isAvailable: true },
    { dayOfWeek: day, startTime: '14:00', endTime: '18:00', isAvailable: true }
  );
}

async function updateDoctors() {
  await mongoose.connect(MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });
  console.log('Connected to MongoDB');

  // Find the Doctor role
  const doctorRole = await Role.findOne({ name: /doctor/i });
  if (!doctorRole) {
    console.error('Doctor role not found');
    process.exit(1);
  }

  // Update all users with Doctor role
  const result = await User.updateMany(
    { role: doctorRole._id },
    { $set: { availability: standardAvailability } }
  );
  console.log(`Updated ${result.nModified || result.modifiedCount || 0} doctors with standard availability.`);
  await mongoose.disconnect();
}

// Map required permissions to correct modules/resources for all dashboard/report endpoints
const permissionMap = [
  { action: 'view', resource: 'dashboard', module: 'dashboard' },
  { action: 'view', resource: 'dashboard', module: 'dashboard' }, // for /dashboard and /recent-activity
  { action: 'view', resource: 'patient-analytics', module: 'reports' },
  { action: 'view', resource: 'appointment-analytics', module: 'reports' },
  { action: 'view', resource: 'financial-analytics', module: 'reports' },
  { action: 'view', resource: 'lab-analytics', module: 'reports' },
  { action: 'view', resource: 'operational', module: 'reports' },
  { action: 'view', resource: 'clinical', module: 'reports' },
  { action: 'view', resource: 'quality', module: 'reports' },
  { action: 'view', resource: 'patients', module: 'patients' },
  { action: 'view', resource: 'appointments', module: 'clinical' },
  { action: 'view', resource: 'reports', module: 'reports' },
];

async function addPermissionsToDoctorRole() {
  await mongoose.connect(MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });
  const doctorRole = await Role.findOne({ name: /doctor/i });
  if (!doctorRole) {
    console.error('Doctor role not found');
    process.exit(1);
  }
  for (const perm of permissionMap) {
    let permission = await Permission.findOne({ action: perm.action, resource: perm.resource, module: perm.module });
    if (!permission) {
      permission = await Permission.create({
        module: perm.module,
        action: perm.action,
        resource: perm.resource,
        description: `Allow view on ${perm.resource}`
      });
    }
    if (!doctorRole.defaultPermissions.map(String).includes(String(permission._id))) {
      doctorRole.defaultPermissions.push(permission._id);
    }
  }
  await doctorRole.save();
  console.log('Updated Doctor role with required permissions.');
  await mongoose.disconnect();
}

// Run both updates
(async () => {
  await updateDoctors();
  await addPermissionsToDoctorRole();
})(); 