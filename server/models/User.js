import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

const UserSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  role: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Role',
    required: true
  },
  permissions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Permission'
  }],
  department: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date
  },
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  availability: [
    {
      dayOfWeek: { type: Number, required: true }, // 0=Sunday, 1=Monday, etc.
      startTime: { type: String, required: true }, // e.g., '09:00'
      endTime: { type: String, required: true },   // e.g., '17:00'
      isAvailable: { type: Boolean, default: true }
    }
  ]
}, {
  timestamps: true
});

// Hash password before saving
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
UserSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to sync role permissions to user permissions
UserSchema.methods.syncRolePermissions = async function() {
  try {
    const Role = mongoose.model('Role');
    const role = await Role.findById(this.role).populate('defaultPermissions');

    if (role && role.defaultPermissions) {
      this.permissions = role.defaultPermissions.map(permission => permission._id);
      await this.save();
    }
  } catch (error) {
    console.error('Error syncing role permissions:', error);
  }
};

// Static method to sync all users' permissions
UserSchema.statics.syncAllUsersPermissions = async function() {
  try {
    const users = await this.find().populate('role');
    const Role = mongoose.model('Role');

    for (const user of users) {
      if (user.role) {
        const role = await Role.findById(user.role._id).populate('defaultPermissions');
        if (role && role.defaultPermissions) {
          user.permissions = role.defaultPermissions.map(permission => permission._id);
          await user.save();
        }
      }
    }
  } catch (error) {
    console.error('Error syncing all users permissions:', error);
  }
};

export default mongoose.model('User', UserSchema);
