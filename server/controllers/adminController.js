import bcrypt from 'bcryptjs';
import mongoose from 'mongoose';
import User from '../models/User.js';
import Role from '../models/Role.js';
import Permission from '../models/Permission.js';
import AuditLog from '../models/AuditLog.js';
import { initializePermissions, initializeRoles, SYSTEM_PERMISSIONS, SYSTEM_ROLES, getPermissionsByCategory } from '../utils/permissionSystem.js';

// Department list - this could be moved to a separate model/config
const DEPARTMENTS = [
  'General Medicine',
  'Cardiology',
  'Neurology',
  'Orthopedics',
  'Pediatrics',
  'Gynecology',
  'Dermatology',
  'Psychiatry',
  'Oncology',
  'Emergency',
  'Surgery',
  'Radiology',
  'Pathology',
  'Administration',
  'Nursing',
  'Pharmacy',
  'Laboratory',
  'ICU',
  'CCU',
  'NICU'
];

// Get existing models with better error handling
const getModels = () => {
  const models = {};

  try {
    models.User = mongoose.model('User');
  } catch (error) {
    console.warn('User model not available:', error.message);
  }

  // Use imported models
  models.Role = Role;
  models.Permission = Permission;
  models.AuditLog = AuditLog;

  try {
    models.Patient = mongoose.model('Patient');
  } catch (error) {
    console.warn('Patient model not available:', error.message);
  }

  try {
    models.Appointment = mongoose.model('Appointment');
  } catch (error) {
    console.warn('Appointment model not available:', error.message);
  }

  return models;
};

// Helper function to generate employee ID
const generateEmployeeId = async () => {
  const models = getModels();
  if (!models.User) return 'EMP001';

  const lastUser = await models.User.findOne({}, {}, { sort: { 'createdAt': -1 } });
  if (!lastUser || !lastUser.employeeId) {
    return 'EMP001';
  }

  const lastNumber = parseInt(lastUser.employeeId.replace('EMP', ''));
  const nextNumber = lastNumber + 1;
  return `EMP${nextNumber.toString().padStart(3, '0')}`;
};

// Helper function to create default roles
const createDefaultRoles = async () => {
  const models = getModels();
  if (!models.Role) {
    console.warn('Role model not available, skipping default role creation');
    return;
  }

  const defaultRoles = [
    {
      name: 'Administrator',
      description: 'Full system access with all administrative privileges',
      permissions: [
        'USER_MANAGEMENT',
        'ROLE_MANAGEMENT',
        'PATIENT_MANAGEMENT',
        'APPOINTMENT_MANAGEMENT',
        'BILLING_MANAGEMENT',
        'REPORT_GENERATION',
        'FACILITY_MANAGEMENT',
        'LABORATORY_MANAGEMENT',
        'PHARMACY_MANAGEMENT',
        'SYSTEM_ADMINISTRATION',
        'AUDIT_LOG_ACCESS',
        'BACKUP_RESTORE',
        'NOTIFICATION_MANAGEMENT'
      ],
      level: 10,
      isSystemRole: true,
      isActive: true
    },
    {
      name: 'Doctor',
      description: 'Medical staff with patient care and medical record access',
      permissions: [
        'PATIENT_MANAGEMENT',
        'APPOINTMENT_MANAGEMENT',
        'LABORATORY_MANAGEMENT',
        'PHARMACY_MANAGEMENT',
        'REPORT_GENERATION'
      ],
      level: 7,
      isSystemRole: true,
      isActive: true
    },
    {
      name: 'Nurse',
      description: 'Nursing staff with patient care and medication management',
      permissions: [
        'PATIENT_MANAGEMENT',
        'PHARMACY_MANAGEMENT',
        'LABORATORY_MANAGEMENT'
      ],
      level: 5,
      isSystemRole: true,
      isActive: true
    },
    {
      name: 'Receptionist',
      description: 'Front desk staff with appointment and basic patient management',
      permissions: [
        'APPOINTMENT_MANAGEMENT',
        'PATIENT_MANAGEMENT'
      ],
      level: 3,
      isSystemRole: true,
      isActive: true
    }
  ];

  try {
    for (const roleData of defaultRoles) {
      const existingRole = await models.Role.findOne({ name: roleData.name });
      if (!existingRole) {
        await models.Role.create(roleData);
        console.log(`✅ Created default role: ${roleData.name}`);
      } else {
        console.log(`ℹ️  Role already exists: ${roleData.name}`);
      }
    }
  } catch (error) {
    console.error('Error creating default roles:', error.message);
  }
};

// Available permissions with descriptions
const availablePermissions = {
  'USER_MANAGEMENT': {
    name: 'User Management',
    description: 'Create, edit, delete, and manage user accounts',
    category: 'Administration'
  },
  'ROLE_MANAGEMENT': {
    name: 'Role Management',
    description: 'Create, edit, delete, and manage user roles and permissions',
    category: 'Administration'
  },
  'PATIENT_MANAGEMENT': {
    name: 'Patient Management',
    description: 'Access and manage patient records and information',
    category: 'Clinical'
  },
  'APPOINTMENT_MANAGEMENT': {
    name: 'Appointment Management',
    description: 'Schedule, modify, and manage patient appointments',
    category: 'Clinical'
  },
  'BILLING_MANAGEMENT': {
    name: 'Billing Management',
    description: 'Manage billing, invoices, and financial records',
    category: 'Financial'
  },
  'REPORT_GENERATION': {
    name: 'Report Generation',
    description: 'Generate and access various system reports',
    category: 'Analytics'
  },
  'FACILITY_MANAGEMENT': {
    name: 'Facility Management',
    description: 'Manage hospital facilities, rooms, and equipment',
    category: 'Operations'
  },
  'LABORATORY_MANAGEMENT': {
    name: 'Laboratory Management',
    description: 'Manage lab tests, results, and laboratory operations',
    category: 'Clinical'
  },
  'PHARMACY_MANAGEMENT': {
    name: 'Pharmacy Management',
    description: 'Manage medications, prescriptions, and pharmacy inventory',
    category: 'Clinical'
  },
  'SYSTEM_ADMINISTRATION': {
    name: 'System Administration',
    description: 'Full system administration and configuration access',
    category: 'Administration'
  },
  'AUDIT_LOG_ACCESS': {
    name: 'Audit Log Access',
    description: 'View and manage system audit logs and activity tracking',
    category: 'Administration'
  },
  'BACKUP_RESTORE': {
    name: 'Backup & Restore',
    description: 'Perform system backups and data restoration',
    category: 'Administration'
  },
  'NOTIFICATION_MANAGEMENT': {
    name: 'Notification Management',
    description: 'Manage system notifications and alerts',
    category: 'Operations'
  }
};

// @desc    Get all users (Admin only)
// @route   GET /api/admin/users
// @access  Private/Admin
export const getAllUsers = async (req, res) => {
  try {
    const models = getModels();
    if (!models.User) {
      return res.status(500).json({
        success: false,
        error: 'User model not available'
      });
    }

    const { page = 1, limit = 20, search, role, status, department } = req.query;

    // Build query
    let query = {};

    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { firstName: searchRegex },
        { lastName: searchRegex },
        { email: searchRegex },
        { employeeId: searchRegex }
      ];
    }

    if (status && status !== 'all') {
      query.isActive = status === 'Active';
    }

    if (department && department !== 'all') {
      query.department = department;
    }

    // Build aggregation pipeline
    const pipeline = [
      { $match: query },
      {
        $lookup: {
          from: 'roles',
          localField: 'role',
          foreignField: '_id',
          as: 'roleInfo'
        }
      },
      {
        $unwind: {
          path: '$roleInfo',
          preserveNullAndEmptyArrays: true
        }
      }
    ];

    // Add role filter if specified
    if (role && role !== 'all') {
      let roleId = null;
      if (mongoose.Types.ObjectId.isValid(role)) {
        roleId = role;
      } else {
        // Try to look up role by name (case-insensitive)
        const roleDoc = await models.Role.findOne({ name: new RegExp('^' + role + '$', 'i') });
        if (roleDoc) {
          roleId = roleDoc._id;
        } else {
          return res.status(400).json({ success: false, error: 'Invalid role specified: ' + role });
        }
      }
      pipeline.push({ $match: { 'roleInfo._id': new mongoose.Types.ObjectId(roleId) } });
    }

    // Get total count for pagination
    const totalCountPipeline = [...pipeline];
    totalCountPipeline.push({ $count: 'total' });
    const totalResult = await models.User.aggregate(totalCountPipeline);
    const totalUsers = totalResult.length > 0 ? totalResult[0].total : 0;

    // Add pagination
    const skip = (page - 1) * limit;
    pipeline.push(
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: parseInt(limit) }
    );

    // Execute aggregation
    const users = await models.User.aggregate(pipeline);

    // Format user data
    const formattedUsers = users.map(user => ({
      _id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.roleInfo || { name: 'Unknown' },
      department: user.department,
      status: user.isActive ? 'Active' : 'Inactive',
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      phone: user.phone,
      employeeId: user.employeeId,
      position: user.position,
      availability: user.availability || [] // <-- ensure this is included
    }));

    res.status(200).json({
      success: true,
      data: formattedUsers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalUsers,
        pages: Math.ceil(totalUsers / limit)
      }
    });
  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching users'
    });
  }
};

// @desc    Create new user (Admin only)
// @route   POST /api/admin/users
// @access  Private/Admin
export const createUser = async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      password,
      role,
      department,
      phone,
      employeeId,
      position,
      specialization,
      licenseNumber
    } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !password || !role) {
      return res.status(400).json({
        success: false,
        error: 'Required fields: firstName, lastName, email, password, role'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email already exists'
      });
    }

    // Check if employeeId already exists
    if (employeeId) {
      const existingEmployeeId = await User.findOne({ employeeId });
      if (existingEmployeeId) {
        return res.status(400).json({
          success: false,
          error: 'Employee ID already exists'
        });
      }
    }

    // Find the role by ID or name
    let userRole;
    if (mongoose.Types.ObjectId.isValid(role)) {
      userRole = await Role.findById(role);
    } else {
      userRole = await Role.findOne({ name: role });
    }

    if (!userRole) {
      return res.status(400).json({
        success: false,
        error: 'Invalid role specified'
      });
    }

    // Generate employee ID if not provided
    const finalEmployeeId = employeeId || `EMP${Date.now().toString().slice(-6)}`;

    // Generate username from email (part before @)
    const username = email.split('@')[0].toLowerCase();

    // Create new user (password will be hashed by the pre-save hook)
    const newUser = new User({
      username,
      firstName,
      lastName,
      email,
      password, // Don't hash here - let the pre-save hook handle it
      role: userRole._id,
      department: department || 'General',
      phone: phone || '',
      employeeId: finalEmployeeId,
      position: position || '',
      specialization: specialization || '',
      licenseNumber: licenseNumber || ''
    });

    await newUser.save();

    // Sync role permissions to user
    await newUser.syncRolePermissions();

    // Populate role information and remove password from response
    const populatedUser = await User.findById(newUser._id)
      .populate('role', 'name description level')
      .populate('permissions')
      .select('-password');

    res.status(201).json({
      success: true,
      data: populatedUser
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating user'
    });
  }
};

// @desc    Update user (Admin only)
// @route   PUT /api/admin/users/:id
// @access  Private/Admin
export const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const models = getModels();

    if (!models.User) {
      return res.status(500).json({
        success: false,
        error: 'User model not available'
      });
    }

    // Remove password from update data (use separate endpoint)
    delete updateData.password;

    // Find user
    const user = await models.User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Check if email is being changed and if it already exists
    if (updateData.email && updateData.email !== user.email) {
      const existingUser = await models.User.findOne({ 
        email: updateData.email, 
        _id: { $ne: id } 
      });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: 'Email already exists'
        });
      }
    }

    // Check if employeeId is being changed and if it already exists
    if (updateData.employeeId && updateData.employeeId !== user.employeeId) {
      const existingEmployeeId = await models.User.findOne({ 
        employeeId: updateData.employeeId, 
        _id: { $ne: id } 
      });
      if (existingEmployeeId) {
        return res.status(400).json({
          success: false,
          error: 'Employee ID already exists'
        });
      }
    }

    // Validate role if being changed
    if (updateData.role) {
      const role = await models.Role.findById(updateData.role);
      if (!role) {
        return res.status(400).json({
          success: false,
          error: 'Invalid role'
        });
      }
    }

    // Update user
    const updatedUser = await models.User.findByIdAndUpdate(
      id,
      { 
        ...updateData,
        updatedAt: new Date()
      },
      { 
        new: true, 
        runValidators: true 
      }
    ).populate('role', 'name description level');

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Format user data for response
    const formattedUser = {
      _id: updatedUser._id,
      firstName: updatedUser.firstName,
      lastName: updatedUser.lastName,
      email: updatedUser.email,
      role: updatedUser.role || { name: 'Unknown' },
      department: updatedUser.department,
      status: updatedUser.isActive ? 'Active' : 'Inactive',
      lastLogin: updatedUser.lastLogin,
      createdAt: updatedUser.createdAt,
      updatedAt: updatedUser.updatedAt,
      phone: updatedUser.phone,
      employeeId: updatedUser.employeeId,
      position: updatedUser.position
    };

    res.status(200).json({
      success: true,
      data: formattedUser
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating user'
    });
  }
};

// @desc    Delete user (Admin only)
// @route   DELETE /api/admin/users/:id
// @access  Private/Admin
export const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;
    const models = getModels();

    if (!models.User) {
      return res.status(500).json({
        success: false,
        error: 'User model not available'
      });
    }

    // Don't allow deletion of own account
    if (id === req.user?.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete your own account'
      });
    }

    // Find user
    const user = await models.User.findById(id).populate('role');
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Don't allow deletion of the last administrator
    if (user.role && user.role.name === 'Administrator') {
      const adminCount = await models.User.countDocuments({
        role: user.role._id,
        isActive: true
      });
      if (adminCount <= 1) {
        return res.status(400).json({
          success: false,
          error: 'Cannot delete the last administrator'
        });
      }
    }

    // Remove user from database
    await models.User.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting user'
    });
  }
};

// @desc    Reset user password (Admin only)
// @route   PUT /api/admin/users/:id/reset-password
// @access  Private/Admin
export const resetUserPassword = async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while resetting password'
    });
  }
};

// @desc    Toggle user status (Admin only)
// @route   PUT /api/admin/users/:id/toggle-status
// @access  Private/Admin
export const toggleUserStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const models = getModels();

    if (!models.User) {
      return res.status(500).json({
        success: false,
        error: 'User model not available'
      });
    }

    // Don't allow deactivation of own account
    if (id === req.user?.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot deactivate your own account'
      });
    }

    // Find user
    const user = await models.User.findById(id).populate('role');
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Don't allow deactivation of the last active administrator
    if (user.role && user.role.name === 'Administrator') {
      const activeAdminCount = await models.User.countDocuments({
        role: user.role._id,
        isActive: true
      });
      if (activeAdminCount <= 1) {
        return res.status(400).json({
          success: false,
          error: 'Cannot deactivate the last active administrator'
        });
      }
    }

    // Toggle status
    user.isActive = !user.isActive;
    user.updatedAt = new Date();
    await user.save();

    // Format user data for response
    const formattedUser = {
      _id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role || { name: 'Unknown' },
      department: user.department,
      status: user.isActive ? 'Active' : 'Inactive',
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      phone: user.phone,
      employeeId: user.employeeId,
      position: user.position
    };

    res.status(200).json({
      success: true,
      data: formattedUser
    });
  } catch (error) {
    console.error('Toggle user status error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating user status'
    });
  }
};

// @desc    Get user statistics (Admin only)
// @route   GET /api/admin/users/stats
// @access  Private/Admin
// @desc    Get all roles
// @route   GET /api/admin/roles
// @access  Private/Admin
export const getAllRoles = async (req, res) => {
  try {
    // Get roles from database
    const roles = await Role.find({})
      .sort({ name: 1 })
      .lean();

    // Add user count for each role
    const rolesWithCount = await Promise.all(
      roles.map(async (role) => {
        let userCount = 0;
        try {
          const User = mongoose.model('User');
          userCount = await User.countDocuments({ role: role._id });
        } catch (err) {
          console.warn('Could not count users for role:', err.message);
        }

        return {
          ...role,
          userCount
        };
      })
    );

    res.status(200).json({
      success: true,
      data: rolesWithCount
    });
  } catch (error) {
    console.error('Get all roles error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching roles'
    });
  }
};

export const getUserStats = async (req, res) => {
  try {
    const models = getModels();
    if (!models.User) {
      return res.status(500).json({
        success: false,
        error: 'User model not available'
      });
    }

    // Get basic user counts
    const totalUsers = await models.User.countDocuments({});
    const activeUsers = await models.User.countDocuments({ isActive: true });
    const inactiveUsers = await models.User.countDocuments({ isActive: false });

    // Get users by role
    const usersByRole = await models.User.aggregate([
      {
        $lookup: {
          from: 'roles',
          localField: 'role',
          foreignField: '_id',
          as: 'roleInfo'
        }
      },
      {
        $unwind: {
          path: '$roleInfo',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $group: {
          _id: '$roleInfo.name',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Get recent logins (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentLogins = await models.User.countDocuments({
      lastLogin: { $gte: sevenDaysAgo }
    });

    res.status(200).json({
      success: true,
      data: {
        totalUsers,
        activeUsers,
        inactiveUsers,
        usersByRole,
        recentLogins
      }
    });
  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching user statistics'
    });
  }
};



// @desc    Create new role (Admin only)
// @route   POST /api/admin/roles
// @access  Private/Admin
export const createRole = async (req, res) => {
  try {
    const { name, description, permissions, level } = req.body;

    // Validation
    if (!name || !description) {
      return res.status(400).json({
        success: false,
        error: 'Name and description are required'
      });
    }

    // Validate permissions if provided
    if (permissions && Array.isArray(permissions)) {
      const validPermissions = [
        'USER_MANAGEMENT',
        'ROLE_MANAGEMENT',
        'PATIENT_MANAGEMENT',
        'APPOINTMENT_MANAGEMENT',
        'BILLING_MANAGEMENT',
        'REPORT_GENERATION',
        'FACILITY_MANAGEMENT',
        'LABORATORY_MANAGEMENT',
        'PHARMACY_MANAGEMENT',
        'SYSTEM_ADMINISTRATION',
        'AUDIT_LOG_ACCESS',
        'BACKUP_RESTORE',
        'NOTIFICATION_MANAGEMENT'
      ];

      const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
      if (invalidPermissions.length > 0) {
        return res.status(400).json({
          success: false,
          error: `Invalid permissions: ${invalidPermissions.join(', ')}`
        });
      }
    }

    // Check if role already exists
    const existingRole = await Role.findOne({
      name: { $regex: new RegExp(`^${name.trim()}$`, 'i') }
    });

    if (existingRole) {
      return res.status(400).json({
        success: false,
        error: 'Role with this name already exists'
      });
    }

    // Create new role
    const newRole = new Role({
      name: name.trim(),
      description: description.trim(),
      defaultPermissions: permissions || [],
      level: level || 1,
      isSystemRole: false
    });

    await newRole.save();

    // Create audit log
    if (req.user) {
      await AuditLog.createLog({
        user: req.user._id,
        action: 'CREATE_ROLE',
        resource: 'Role',
        resourceId: newRole._id.toString(),
        details: `Created new role: ${newRole.name}`,
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        status: 'Success'
      });
    }

    res.status(201).json({
      success: true,
      data: newRole
    });
  } catch (error) {
    console.error('Create role error:', error);

    // Create audit log for failed attempt
    if (req.user) {
      try {
        await AuditLog.createLog({
          user: req.user._id,
          action: 'CREATE_ROLE',
          resource: 'Role',
          details: `Failed to create role: ${req.body.name || 'Unknown'}`,
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.get('User-Agent'),
          status: 'Failed'
        });
      } catch (auditError) {
        console.error('Failed to create audit log:', auditError);
      }
    }

    res.status(500).json({
      success: false,
      error: 'Server error while creating role'
    });
  }
};

// @desc    Update role (Admin only)
// @route   PUT /api/admin/roles/:id
// @access  Private/Admin
export const updateRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, permissions } = req.body;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid role ID'
      });
    }

    // Find role
    const role = await Role.findById(id);
    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      });
    }

    // Don't allow modification of system roles' core properties
    if (role.isSystemRole && name && name.trim() !== role.name) {
      return res.status(400).json({
        success: false,
        error: 'Cannot modify system role name'
      });
    }

    // Validation for name uniqueness
    if (name && name.trim() !== role.name) {
      const existingRole = await Role.findOne({
        name: { $regex: new RegExp(`^${name.trim()}$`, 'i') },
        _id: { $ne: id }
      });
      if (existingRole) {
        return res.status(400).json({
          success: false,
          error: 'Role with this name already exists'
        });
      }
    }

    // Validate permissions
    if (permissions && Array.isArray(permissions)) {
      const validPermissions = [
        'USER_MANAGEMENT',
        'ROLE_MANAGEMENT',
        'PATIENT_MANAGEMENT',
        'APPOINTMENT_MANAGEMENT',
        'BILLING_MANAGEMENT',
        'REPORT_GENERATION',
        'FACILITY_MANAGEMENT',
        'LABORATORY_MANAGEMENT',
        'PHARMACY_MANAGEMENT',
        'SYSTEM_ADMINISTRATION',
        'AUDIT_LOG_ACCESS',
        'BACKUP_RESTORE',
        'NOTIFICATION_MANAGEMENT'
      ];

      const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
      if (invalidPermissions.length > 0) {
        return res.status(400).json({
          success: false,
          error: `Invalid permissions: ${invalidPermissions.join(', ')}`
        });
      }
    }

    // Update role
    const updateData = {};
    if (name) updateData.name = name.trim();
    if (description) updateData.description = description.trim();
    if (permissions) updateData.defaultPermissions = permissions;

    const updatedRole = await Role.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    // Sync permissions for all users with this role
    try {
      const User = mongoose.model('User');
      const usersWithRole = await User.find({ role: id });
      for (const user of usersWithRole) {
        await user.syncRolePermissions();
      }
    } catch (syncError) {
      console.error('Error syncing user permissions after role update:', syncError);
    }

    // Create audit log
    if (req.user) {
      await AuditLog.createLog({
        user: req.user._id,
        action: 'UPDATE_ROLE',
        resource: 'Role',
        resourceId: updatedRole._id.toString(),
        details: `Updated role: ${updatedRole.name}`,
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        status: 'Success'
      });
    }

    res.status(200).json({
      success: true,
      data: updatedRole
    });
  } catch (error) {
    console.error('Update role error:', error);

    // Create audit log for failed attempt
    if (req.user) {
      try {
        await AuditLog.createLog({
          user: req.user._id,
          action: 'UPDATE_ROLE',
          resource: 'Role',
          resourceId: req.params.id,
          details: `Failed to update role: ${req.body.name || 'Unknown'}`,
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.get('User-Agent'),
          status: 'Failed'
        });
      } catch (auditError) {
        console.error('Failed to create audit log:', auditError);
      }
    }

    res.status(500).json({
      success: false,
      error: 'Server error while updating role'
    });
  }
};

// @desc    Delete role (Admin only)
// @route   DELETE /api/admin/roles/:id
// @access  Private/Admin
export const deleteRole = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid role ID'
      });
    }

    // Find role
    const role = await Role.findById(id);
    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      });
    }

    // Don't allow deletion of system roles
    if (role.isSystemRole) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete system roles'
      });
    }

    // Check if any users have this role
    try {
      const User = mongoose.model('User');
      const usersWithRole = await User.find({ role: id });
      if (usersWithRole.length > 0) {
        return res.status(400).json({
          success: false,
          error: `Cannot delete role. ${usersWithRole.length} users are assigned to this role.`
        });
      }
    } catch (userError) {
      console.warn('Could not check users for role:', userError.message);
    }

    // Delete role
    await Role.findByIdAndDelete(id);

    // Create audit log
    if (req.user) {
      await AuditLog.createLog({
        user: req.user._id,
        action: 'DELETE_ROLE',
        resource: 'Role',
        resourceId: id,
        details: `Deleted role: ${role.name}`,
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        status: 'Success'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    console.error('Delete role error:', error);

    // Create audit log for failed attempt
    if (req.user) {
      try {
        await AuditLog.createLog({
          user: req.user._id,
          action: 'DELETE_ROLE',
          resource: 'Role',
          resourceId: req.params.id,
          details: `Failed to delete role`,
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.get('User-Agent'),
          status: 'Failed'
        });
      } catch (auditError) {
        console.error('Failed to create audit log:', auditError);
      }
    }

    res.status(500).json({
      success: false,
      error: 'Server error while deleting role'
    });
  }
};

// @desc    Get audit logs (Admin only)
// @route   GET /api/admin/audit-logs
// @access  Private/Admin
export const getAuditLogs = async (req, res) => {
  try {
    const { page = 1, limit = 50, action, user, status, days = 30 } = req.query;

    // Build query
    let query = {};

    if (action) {
      query.action = new RegExp(action, 'i');
    }

    if (user) {
      query.user = user;
    }

    if (status) {
      query.status = status;
    }

    // Filter by date range
    if (days) {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(days));
      query.timestamp = { $gte: startDate };
    }

    // Get total count
    const totalLogs = await AuditLog.countDocuments(query);

    // Get paginated results
    const skip = (page - 1) * limit;
    const auditLogs = await AuditLog.find(query)
      .populate('user', 'firstName lastName email')
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    res.status(200).json({
      success: true,
      data: auditLogs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalLogs,
        pages: Math.ceil(totalLogs / limit)
      }
    });
  } catch (error) {
    console.error('Get audit logs error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching audit logs'
    });
  }
};

// @desc    Create audit log entry
// @route   POST /api/admin/audit-logs
// @access  Private
export const createAuditLog = async (req, res) => {
  try {
    const { action, resource, resourceId, details } = req.body;

    // Validation
    if (!action) {
      return res.status(400).json({
        success: false,
        error: 'Action is required'
      });
    }

    const auditLogData = {
      user: req.user?._id,
      action,
      resource,
      resourceId,
      details,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      status: 'Success'
    };

    const newAuditLog = await AuditLog.createLog(auditLogData);

    res.status(201).json({
      success: true,
      data: newAuditLog
    });
  } catch (error) {
    console.error('Create audit log error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating audit log'
    });
  }
};

// @desc    Get system statistics (Admin only)
// @route   GET /api/admin/system-stats
// @access  Private/Admin
export const getSystemStats = async (req, res) => {
  try {
    const models = getModels();

    // Get real statistics from database
    const [totalUsers, totalRoles, activeUsers] = await Promise.all([
      models.User?.countDocuments() || 0,
      models.Role?.countDocuments() || 0,
      models.User?.countDocuments({ isActive: true }) || 0
    ]);

    const systemStats = {
      totalUsers,
      totalRoles,
      activeUsers,
      recentActivity: totalUsers, // Could be enhanced with actual activity tracking
      recentLogins: Math.floor(activeUsers * 0.7), // Estimated based on active users
      systemUptime: process.uptime(),
      timestamp: new Date()
    };

    res.status(200).json({
      success: true,
      data: systemStats
    });
  } catch (error) {
    console.error('Get system stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching system statistics'
    });
  }
};

// @desc    Get departments list
// @route   GET /api/admin/departments
// @access  Public
export const getDepartments = async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      data: DEPARTMENTS
    });
  } catch (error) {
    console.error('Get departments error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching departments'
    });
  }
};

// ===== PERMISSION MANAGEMENT =====

// @desc    Get all available permissions
// @route   GET /api/admin/permissions
// @access  Private/Admin
export const getAvailablePermissions = async (req, res) => {
  try {
    // Group permissions by category
    const permissionsByCategory = {};

    Object.entries(availablePermissions).forEach(([key, permission]) => {
      const category = permission.category;
      if (!permissionsByCategory[category]) {
        permissionsByCategory[category] = [];
      }
      permissionsByCategory[category].push({
        key,
        ...permission
      });
    });

    res.status(200).json({
      success: true,
      data: {
        permissions: availablePermissions,
        permissionsByCategory
      }
    });
  } catch (error) {
    console.error('Get available permissions error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching permissions'
    });
  }
};

// @desc    Get user permissions
// @route   GET /api/admin/users/:id/permissions
// @access  Private/Admin
export const getUserPermissions = async (req, res) => {
  try {
    const { id } = req.params;

    // Find user
    const user = await User.findById(id).populate('role');
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Find user's role
    const role = mockRoles.find(role => role._id === user.role._id);
    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'User role not found'
      });
    }

    // Get detailed permissions
    const userPermissions = role.permissions.map(permissionKey => ({
      key: permissionKey,
      ...availablePermissions[permissionKey]
    }));

    res.status(200).json({
      success: true,
      data: {
        user: {
          _id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role
        },
        permissions: userPermissions,
        permissionKeys: role.permissions
      }
    });
  } catch (error) {
    console.error('Get user permissions error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching user permissions'
    });
  }
};

// @desc    Check if user has specific permission
// @route   GET /api/admin/users/:id/permissions/:permission
// @access  Private/Admin
export const checkUserPermission = async (req, res) => {
  try {
    const { id, permission } = req.params;

    // Find user
    const user = await User.findById(id).populate('role');
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Find user's role
    const role = mockRoles.find(role => role._id === user.role._id);
    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'User role not found'
      });
    }

    // Check if permission exists
    if (!availablePermissions[permission]) {
      return res.status(400).json({
        success: false,
        error: 'Invalid permission'
      });
    }

    // Check if user has permission
    const hasPermission = role.permissions.includes(permission);

    res.status(200).json({
      success: true,
      data: {
        user: {
          _id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role
        },
        permission: {
          key: permission,
          ...availablePermissions[permission]
        },
        hasPermission
      }
    });
  } catch (error) {
    console.error('Check user permission error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while checking user permission'
    });
  }
};

// ===== ENHANCED PERMISSION SYSTEM =====

// @desc    Initialize system permissions and roles
// @route   POST /api/admin/initialize-system
// @access  Private/Super Admin
export const initializeSystem = async (req, res) => {
  try {
    // Only super admin can initialize system
    if (req.user.role?.level < 10) {
      return res.status(403).json({
        success: false,
        error: 'Only Super Admin can initialize the system'
      });
    }

    console.log('🚀 Starting system initialization...');

    // Initialize permissions and roles
    const permissions = await initializePermissions();
    const roles = await initializeRoles();

    res.status(200).json({
      success: true,
      message: 'System initialized successfully',
      data: {
        permissionsCreated: permissions.length,
        rolesCreated: roles.length
      }
    });
  } catch (error) {
    console.error('System initialization error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initialize system'
    });
  }
};

// @desc    Get all permissions grouped by category
// @route   GET /api/admin/permissions/categories
// @access  Private/Admin
export const getPermissionCategories = async (req, res) => {
  try {
    const categories = getPermissionsByCategory();

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get permission categories error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get permission categories'
    });
  }
};

// @desc    Get all permissions from database
// @route   GET /api/admin/permissions/all
// @access  Private/Admin
export const getAllPermissions = async (req, res) => {
  try {
    const permissions = await Permission.find({})
      .sort({ category: 1, module: 1, action: 1 });

    // Group by category
    const groupedPermissions = {};
    permissions.forEach(permission => {
      const category = permission.category || 'Other';
      if (!groupedPermissions[category]) {
        groupedPermissions[category] = [];
      }
      groupedPermissions[category].push(permission);
    });

    res.status(200).json({
      success: true,
      data: {
        permissions,
        grouped: groupedPermissions,
        total: permissions.length
      }
    });
  } catch (error) {
    console.error('Get all permissions error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get permissions'
    });
  }
};

// @desc    Assign permissions to role
// @route   PUT /api/admin/roles/:id/permissions
// @access  Private/Admin
export const assignPermissionsToRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { permissionIds } = req.body;

    if (!permissionIds || !Array.isArray(permissionIds)) {
      return res.status(400).json({
        success: false,
        error: 'Permission IDs array is required'
      });
    }

    // Find the role
    const role = await Role.findById(id);
    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      });
    }

    // Verify all permission IDs exist
    const permissions = await Permission.find({ _id: { $in: permissionIds } });
    if (permissions.length !== permissionIds.length) {
      return res.status(400).json({
        success: false,
        error: 'Some permission IDs are invalid'
      });
    }

    // Update role permissions
    role.defaultPermissions = permissionIds;
    await role.save();

    // Sync permissions for all users with this role
    const User = mongoose.model('User');
    await User.updateMany(
      { role: role._id },
      { permissions: permissionIds }
    );

    const updatedRole = await Role.findById(id)
      .populate('defaultPermissions');

    res.status(200).json({
      success: true,
      message: 'Permissions assigned to role successfully',
      data: updatedRole
    });
  } catch (error) {
    console.error('Assign permissions to role error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to assign permissions to role'
    });
  }
};

// @desc    Assign role to user
// @route   PUT /api/admin/users/:id/role
// @access  Private/Admin
export const assignRoleToUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { roleId } = req.body;

    if (!roleId) {
      return res.status(400).json({
        success: false,
        error: 'Role ID is required'
      });
    }

    // Find the user
    const User = mongoose.model('User');
    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Find the role
    const role = await Role.findById(roleId).populate('defaultPermissions');
    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      });
    }

    // Update user role and sync permissions
    user.role = roleId;
    user.permissions = role.defaultPermissions.map(p => p._id);
    await user.save();

    const updatedUser = await User.findById(id)
      .populate('role')
      .populate('permissions')
      .select('-password');

    res.status(200).json({
      success: true,
      message: 'Role assigned to user successfully',
      data: updatedUser
    });
  } catch (error) {
    console.error('Assign role to user error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to assign role to user'
    });
  }
};

// @desc    Bulk user operations
// @route   POST /api/admin/users/bulk
// @access  Private/Admin
export const bulkUserOperations = async (req, res) => {
  try {
    const { operation, userIds, data } = req.body;

    if (!operation || !userIds || !Array.isArray(userIds)) {
      return res.status(400).json({
        success: false,
        error: 'Operation and user IDs array are required'
      });
    }

    const User = mongoose.model('User');
    let result = {};

    switch (operation) {
      case 'activate':
        result = await User.updateMany(
          { _id: { $in: userIds } },
          { isActive: true }
        );
        break;

      case 'deactivate':
        result = await User.updateMany(
          { _id: { $in: userIds } },
          { isActive: false }
        );
        break;

      case 'assignRole':
        if (!data?.roleId) {
          return res.status(400).json({
            success: false,
            error: 'Role ID is required for role assignment'
          });
        }

        // Get role permissions
        const role = await Role.findById(data.roleId).populate('defaultPermissions');
        if (!role) {
          return res.status(404).json({
            success: false,
            error: 'Role not found'
          });
        }

        result = await User.updateMany(
          { _id: { $in: userIds } },
          {
            role: data.roleId,
            permissions: role.defaultPermissions.map(p => p._id)
          }
        );
        break;

      case 'updateDepartment':
        if (!data?.department) {
          return res.status(400).json({
            success: false,
            error: 'Department is required'
          });
        }

        result = await User.updateMany(
          { _id: { $in: userIds } },
          { department: data.department }
        );
        break;

      case 'delete':
        // Soft delete - mark as inactive and add deletion timestamp
        result = await User.updateMany(
          { _id: { $in: userIds } },
          {
            isActive: false,
            deletedAt: new Date(),
            deletedBy: req.user._id
          }
        );
        break;

      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid operation'
        });
    }

    res.status(200).json({
      success: true,
      message: `Bulk ${operation} completed successfully`,
      data: {
        operation,
        affectedUsers: result.modifiedCount || result.deletedCount,
        userIds
      }
    });
  } catch (error) {
    console.error('Bulk user operations error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform bulk operation'
    });
  }
};

// @desc    Get user activity and statistics
// @route   GET /api/admin/users/:id/activity
// @access  Private/Admin
export const getUserActivity = async (req, res) => {
  try {
    const { id } = req.params;
    const { days = 30 } = req.query;

    const User = mongoose.model('User');
    const user = await User.findById(id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Get audit logs for this user
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const auditLogs = await AuditLog.find({
      user: id,
      timestamp: { $gte: startDate }
    }).sort({ timestamp: -1 }).limit(100);

    // Calculate activity statistics
    const stats = {
      totalActions: auditLogs.length,
      successfulActions: auditLogs.filter(log => log.status === 'Success').length,
      failedActions: auditLogs.filter(log => log.status === 'Failed').length,
      lastActivity: user.lastLogin,
      actionsByDay: {}
    };

    // Group actions by day
    auditLogs.forEach(log => {
      const day = log.timestamp.toISOString().split('T')[0];
      if (!stats.actionsByDay[day]) {
        stats.actionsByDay[day] = 0;
      }
      stats.actionsByDay[day]++;
    });

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: `${user.firstName} ${user.lastName}`,
          email: user.email,
          role: user.role,
          department: user.department,
          isActive: user.isActive
        },
        stats,
        recentActivity: auditLogs.slice(0, 20) // Last 20 activities
      }
    });
  } catch (error) {
    console.error('Get user activity error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user activity'
    });
  }
};

// @desc    Get audit statistics
// @route   GET /api/admin/audit-stats
// @access  Private/Admin
export const getAuditStats = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Build date filter
    let dateFilter = {};
    if (startDate || endDate) {
      dateFilter.timestamp = {};
      if (startDate) dateFilter.timestamp.$gte = new Date(startDate);
      if (endDate) dateFilter.timestamp.$lte = new Date(endDate + 'T23:59:59.999Z');
    }

    // Get total logs count
    const totalLogs = await AuditLog.countDocuments(dateFilter);

    // Get successful actions count
    const successfulActions = await AuditLog.countDocuments({
      ...dateFilter,
      status: 'Success'
    });

    // Get failed actions count
    const failedActions = await AuditLog.countDocuments({
      ...dateFilter,
      status: 'Failed'
    });

    // Get unique users count
    const uniqueUsers = await AuditLog.distinct('user', dateFilter);

    // Get top actions
    const topActions = await AuditLog.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$action', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 },
      { $project: { action: '$_id', count: 1, _id: 0 } }
    ]);

    // Get activity by hour
    const activityByHour = await AuditLog.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: { $hour: '$timestamp' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } },
      { $project: { hour: '$_id', count: 1, _id: 0 } }
    ]);

    const stats = {
      totalLogs,
      successfulActions,
      failedActions,
      uniqueUsers: uniqueUsers.length,
      topActions,
      activityByHour
    };

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get audit stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get audit statistics'
    });
  }
};

// @desc    Export audit logs
// @route   GET /api/admin/audit-logs/export
// @access  Private/Admin
export const exportAuditLogs = async (req, res) => {
  try {
    const {
      search,
      user,
      action,
      status,
      resource,
      startDate,
      endDate,
      format = 'csv'
    } = req.query;

    // Build query
    let query = {};

    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { action: searchRegex },
        { details: searchRegex },
        { resource: searchRegex }
      ];
    }

    if (user && user !== 'all') {
      query.user = user;
    }

    if (action && action !== 'all') {
      query.action = action;
    }

    if (status && status !== 'all') {
      query.status = status;
    }

    if (resource && resource !== 'all') {
      query.resource = resource;
    }

    // Date filter
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate + 'T23:59:59.999Z');
    }

    // Get audit logs
    const auditLogs = await AuditLog.find(query)
      .populate('user', 'firstName lastName email')
      .sort({ timestamp: -1 })
      .limit(10000); // Limit to prevent memory issues

    if (format === 'csv') {
      // Generate CSV
      const csvHeader = 'Timestamp,User,Email,Action,Resource,Status,IP Address,Details\n';
      const csvRows = auditLogs.map(log => {
        const user = log.user || {};
        return [
          new Date(log.timestamp).toISOString(),
          `"${user.firstName || ''} ${user.lastName || ''}".trim()`,
          user.email || '',
          log.action || '',
          log.resource || '',
          log.status || '',
          log.ipAddress || '',
          `"${(log.details || '').replace(/"/g, '""')}"`
        ].join(',');
      }).join('\n');

      const csvContent = csvHeader + csvRows;

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.csv"`);
      res.send(csvContent);
    } else {
      // Return JSON
      res.status(200).json({
        success: true,
        data: auditLogs,
        total: auditLogs.length
      });
    }
  } catch (error) {
    console.error('Export audit logs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export audit logs'
    });
  }
};
