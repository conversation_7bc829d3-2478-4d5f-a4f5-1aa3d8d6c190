import mongoose from 'mongoose';
import Notification from '../models/Notification.js';
import dotenv from 'dotenv';

dotenv.config();

const sampleNotifications = [
  {
    recipient: '686bf631a1874b95d56a111e', // Admin user ID
    title: 'New Patient Registration',
    message: 'A new patient has been registered in the system. Please review their information.',
    type: 'info',
    priority: 'medium',
    category: 'patient',
    data: {
      patientId: '686ad92c4d1a9bf488a11a57',
      patientName: '<PERSON>'
    }
  },
  {
    recipient: '686bf631a1874b95d56a111e',
    title: 'Appointment Reminder',
    message: 'You have an upcoming appointment scheduled for tomorrow at 10:00 AM.',
    type: 'appointment',
    priority: 'high',
    category: 'appointment',
    data: {
      appointmentId: '686ada08a68083ced6a7408b',
      appointmentTime: '10:00 AM',
      patientName: '<PERSON>'
    }
  },
  {
    recipient: '686bf631a1874b95d56a111e',
    title: 'System Maintenance',
    message: 'Scheduled system maintenance will occur tonight from 2:00 AM to 4:00 AM.',
    type: 'system',
    priority: 'medium',
    category: 'system',
    data: {
      maintenanceWindow: '2:00 AM - 4:00 AM'
    }
  },
  {
    recipient: '686bf631a1874b95d56a111e',
    title: 'Payment Received',
    message: 'Payment of $250.00 has been received for invoice #INV-001.',
    type: 'success',
    priority: 'low',
    category: 'billing',
    data: {
      amount: 250.00,
      invoiceId: 'INV-001'
    }
  },
  {
    recipient: '686bf631a1874b95d56a111e',
    title: 'Equipment Maintenance Due',
    message: 'MRI Machine #001 is due for scheduled maintenance.',
    type: 'warning',
    priority: 'high',
    category: 'maintenance',
    data: {
      equipmentId: 'EQ-001',
      equipmentName: 'MRI Machine #001'
    }
  },
  {
    recipient: '686bf631a1874b95d56a111e',
    title: 'Lab Results Available',
    message: 'Lab results for patient Sarah Johnson are now available for review.',
    type: 'info',
    priority: 'medium',
    category: 'general',
    data: {
      patientId: '686ad92c4d1a9bf488a11a58',
      patientName: 'Sarah Johnson',
      testType: 'Blood Work'
    }
  }
];

async function seedNotifications() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI;
    console.log('Connected to MongoDB');

    // Clear existing notifications
    await Notification.deleteMany({});
    console.log('Cleared existing notifications');

    // Create sample notifications
    const notifications = await Notification.insertMany(sampleNotifications);
    console.log(`Created ${notifications.length} sample notifications`);

    // Display created notifications
    notifications.forEach((notification, index) => {
      console.log(`${index + 1}. ${notification.title} - ${notification.type} (${notification.priority})`);
    });

    console.log('Notification seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding notifications:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedNotifications();
