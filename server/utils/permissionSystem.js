import mongoose from 'mongoose';
import Permission from '../models/Permission.js';
import Role from '../models/Role.js';

// Enhanced permission system with comprehensive permissions
export const SYSTEM_PERMISSIONS = [
  // Dashboard permissions
  { module: 'dashboard', action: 'view', resource: '*', description: 'View dashboard and analytics', category: 'Dashboard' },
  
  // User management permissions
  { module: 'admin', action: 'view', resource: 'users', description: 'View user accounts', category: 'User Management' },
  { module: 'admin', action: 'create', resource: 'users', description: 'Create new user accounts', category: 'User Management' },
  { module: 'admin', action: 'edit', resource: 'users', description: 'Edit user accounts', category: 'User Management' },
  { module: 'admin', action: 'delete', resource: 'users', description: 'Delete user accounts', category: 'User Management' },
  { module: 'admin', action: 'assign', resource: 'users', description: 'Assign roles to users', category: 'User Management' },
  { module: 'admin', action: 'export', resource: 'users', description: 'Export user data', category: 'User Management' },
  
  // Role management permissions
  { module: 'admin', action: 'view', resource: 'roles', description: 'View roles and permissions', category: 'Role Management' },
  { module: 'admin', action: 'create', resource: 'roles', description: 'Create new roles', category: 'Role Management' },
  { module: 'admin', action: 'edit', resource: 'roles', description: 'Edit existing roles', category: 'Role Management' },
  { module: 'admin', action: 'delete', resource: 'roles', description: 'Delete roles', category: 'Role Management' },
  { module: 'admin', action: 'assign', resource: 'permissions', description: 'Assign permissions to roles', category: 'Role Management' },
  
  // Patient management permissions
  { module: 'patients', action: 'view', resource: '*', description: 'View patient records', category: 'Patient Management' },
  { module: 'patients', action: 'create', resource: '*', description: 'Create patient records', category: 'Patient Management' },
  { module: 'patients', action: 'edit', resource: '*', description: 'Edit patient records', category: 'Patient Management' },
  { module: 'patients', action: 'delete', resource: '*', description: 'Delete patient records', category: 'Patient Management' },
  { module: 'patients', action: 'export', resource: '*', description: 'Export patient data', category: 'Patient Management' },
  
  // Appointment management permissions
  { module: 'appointments', action: 'view', resource: '*', description: 'View appointments', category: 'Appointment Management' },
  { module: 'appointments', action: 'create', resource: '*', description: 'Create appointments', category: 'Appointment Management' },
  { module: 'appointments', action: 'edit', resource: '*', description: 'Edit appointments', category: 'Appointment Management' },
  { module: 'appointments', action: 'delete', resource: '*', description: 'Cancel appointments', category: 'Appointment Management' },
  
  // Clinical permissions
  { module: 'clinical', action: 'view', resource: '*', description: 'View clinical data', category: 'Clinical Management' },
  { module: 'clinical', action: 'create', resource: '*', description: 'Create clinical records', category: 'Clinical Management' },
  { module: 'clinical', action: 'edit', resource: '*', description: 'Edit clinical records', category: 'Clinical Management' },
  { module: 'clinical', action: 'approve', resource: '*', description: 'Approve clinical records', category: 'Clinical Management' },
  
  // Laboratory permissions
  { module: 'laboratory', action: 'view', resource: '*', description: 'View lab results', category: 'Laboratory Management' },
  { module: 'laboratory', action: 'create', resource: '*', description: 'Create lab orders', category: 'Laboratory Management' },
  { module: 'laboratory', action: 'edit', resource: '*', description: 'Edit lab results', category: 'Laboratory Management' },
  { module: 'laboratory', action: 'approve', resource: '*', description: 'Approve lab results', category: 'Laboratory Management' },
  
  // Pharmacy permissions
  { module: 'pharmacy', action: 'view', resource: '*', description: 'View pharmacy data', category: 'Pharmacy Management' },
  { module: 'pharmacy', action: 'create', resource: '*', description: 'Create prescriptions', category: 'Pharmacy Management' },
  { module: 'pharmacy', action: 'edit', resource: '*', description: 'Edit prescriptions', category: 'Pharmacy Management' },
  { module: 'pharmacy', action: 'approve', resource: '*', description: 'Approve prescriptions', category: 'Pharmacy Management' },
  
  // Financial permissions
  { module: 'financial', action: 'view', resource: '*', description: 'View financial data', category: 'Financial Management' },
  { module: 'financial', action: 'create', resource: '*', description: 'Create bills and invoices', category: 'Financial Management' },
  { module: 'financial', action: 'edit', resource: '*', description: 'Edit financial records', category: 'Financial Management' },
  { module: 'financial', action: 'approve', resource: '*', description: 'Approve financial transactions', category: 'Financial Management' },
  
  // HR permissions
  { module: 'hr', action: 'view', resource: '*', description: 'View HR data', category: 'Human Resources' },
  { module: 'hr', action: 'create', resource: '*', description: 'Create HR records', category: 'Human Resources' },
  { module: 'hr', action: 'edit', resource: '*', description: 'Edit HR records', category: 'Human Resources' },
  
  // Facility permissions
  { module: 'facility', action: 'view', resource: '*', description: 'View facility data', category: 'Facility Management' },
  { module: 'facility', action: 'create', resource: '*', description: 'Create facility records', category: 'Facility Management' },
  { module: 'facility', action: 'edit', resource: '*', description: 'Edit facility records', category: 'Facility Management' },
  
  // System administration permissions
  { module: 'admin', action: 'view', resource: 'system', description: 'View system settings', category: 'System Administration' },
  { module: 'admin', action: 'edit', resource: 'system', description: 'Edit system settings', category: 'System Administration' },
  { module: 'admin', action: 'view', resource: 'audit', description: 'View audit logs', category: 'System Administration' },
  { module: 'admin', action: 'export', resource: 'audit', description: 'Export audit logs', category: 'System Administration' },
  { module: 'admin', action: 'view', resource: 'backup', description: 'View backup status', category: 'System Administration' },
  { module: 'admin', action: 'create', resource: 'backup', description: 'Create system backups', category: 'System Administration' },
  
  // Reports permissions
  { module: 'reports', action: 'view', resource: '*', description: 'View reports', category: 'Reports' },
  { module: 'reports', action: 'create', resource: '*', description: 'Create custom reports', category: 'Reports' },
  { module: 'reports', action: 'export', resource: '*', description: 'Export reports', category: 'Reports' }
];

// Enhanced role definitions with proper permission mapping
export const SYSTEM_ROLES = [
  {
    name: 'Super Admin',
    description: 'Complete system access with all administrative privileges and system control',
    level: 10,
    isSystemRole: true,
    permissions: SYSTEM_PERMISSIONS.map(p => `${p.module}:${p.action}:${p.resource}`)
  },
  {
    name: 'Administrator',
    description: 'Administrative access with user and system management capabilities',
    level: 9,
    isSystemRole: true,
    permissions: [
      'admin:view:users', 'admin:create:users', 'admin:edit:users', 'admin:delete:users', 'admin:assign:users', 'admin:export:users',
      'admin:view:roles', 'admin:create:roles', 'admin:edit:roles', 'admin:assign:permissions',
      'dashboard:view:*',
      'patients:view:*', 'patients:create:*', 'patients:edit:*', 'patients:export:*',
      'appointments:view:*', 'appointments:create:*', 'appointments:edit:*',
      'financial:view:*', 'financial:create:*', 'financial:edit:*',
      'reports:view:*', 'reports:export:*',
      'admin:view:audit', 'admin:export:audit'
    ]
  },
  {
    name: 'Doctor',
    description: 'Medical staff with patient care and clinical record access',
    level: 7,
    isSystemRole: true,
    permissions: [
      'dashboard:view:*',
      'patients:view:*', 'patients:create:*', 'patients:edit:*',
      'appointments:view:*', 'appointments:create:*', 'appointments:edit:*',
      'clinical:view:*', 'clinical:create:*', 'clinical:edit:*', 'clinical:approve:*',
      'laboratory:view:*', 'laboratory:create:*', 'laboratory:edit:*',
      'pharmacy:view:*', 'pharmacy:create:*', 'pharmacy:edit:*',
      'reports:view:*'
    ]
  },
  {
    name: 'Nurse',
    description: 'Nursing staff with patient care and medication management',
    level: 5,
    isSystemRole: true,
    permissions: [
      'dashboard:view:*',
      'patients:view:*', 'patients:edit:*',
      'appointments:view:*',
      'clinical:view:*', 'clinical:create:*', 'clinical:edit:*',
      'laboratory:view:*',
      'pharmacy:view:*', 'pharmacy:edit:*'
    ]
  },
  {
    name: 'Receptionist',
    description: 'Front desk staff with appointment and basic patient management',
    level: 3,
    isSystemRole: true,
    permissions: [
      'dashboard:view:*',
      'patients:view:*', 'patients:create:*', 'patients:edit:*',
      'appointments:view:*', 'appointments:create:*', 'appointments:edit:*'
    ]
  },
  {
    name: 'Lab Technician',
    description: 'Laboratory staff with lab test and result management',
    level: 4,
    isSystemRole: true,
    permissions: [
      'dashboard:view:*',
      'patients:view:*',
      'laboratory:view:*', 'laboratory:create:*', 'laboratory:edit:*'
    ]
  },
  {
    name: 'Pharmacist',
    description: 'Pharmacy staff with medication and prescription management',
    level: 4,
    isSystemRole: true,
    permissions: [
      'dashboard:view:*',
      'patients:view:*',
      'pharmacy:view:*', 'pharmacy:create:*', 'pharmacy:edit:*', 'pharmacy:approve:*'
    ]
  }
];

// Initialize permissions in database
export const initializePermissions = async () => {
  try {
    console.log('🔧 Initializing system permissions...');
    
    const createdPermissions = [];
    for (const permData of SYSTEM_PERMISSIONS) {
      try {
        const existingPerm = await Permission.findOne({
          module: permData.module,
          action: permData.action,
          resource: permData.resource
        });

        if (!existingPerm) {
          const permission = await Permission.create({
            ...permData,
            isSystemPermission: true
          });
          createdPermissions.push(permission);
          console.log(`✅ Created permission: ${permData.module}:${permData.action}:${permData.resource}`);
        } else {
          createdPermissions.push(existingPerm);
        }
      } catch (error) {
        console.error(`❌ Error creating permission ${permData.module}:${permData.action}:${permData.resource}:`, error.message);
      }
    }

    console.log(`✅ Permissions initialized: ${createdPermissions.length} total`);
    return createdPermissions;
  } catch (error) {
    console.error('❌ Error initializing permissions:', error);
    throw error;
  }
};

// Initialize roles with proper permission references
export const initializeRoles = async () => {
  try {
    console.log('🔧 Initializing system roles...');
    
    // First ensure permissions exist
    await initializePermissions();
    
    const createdRoles = [];
    for (const roleData of SYSTEM_ROLES) {
      try {
        const existingRole = await Role.findOne({ name: roleData.name });
        
        if (!existingRole) {
          // Get permission IDs for this role
          const permissionIds = [];
          for (const permString of roleData.permissions) {
            const [module, action, resource] = permString.split(':');
            const permission = await Permission.findOne({ module, action, resource });
            if (permission) {
              permissionIds.push(permission._id);
            }
          }
          
          const role = await Role.create({
            name: roleData.name,
            description: roleData.description,
            level: roleData.level,
            isSystemRole: roleData.isSystemRole,
            defaultPermissions: permissionIds
          });
          
          createdRoles.push(role);
          console.log(`✅ Created role: ${roleData.name} with ${permissionIds.length} permissions`);
        } else {
          createdRoles.push(existingRole);
          console.log(`ℹ️  Role already exists: ${roleData.name}`);
        }
      } catch (error) {
        console.error(`❌ Error creating role ${roleData.name}:`, error.message);
      }
    }

    console.log(`✅ Roles initialized: ${createdRoles.length} total`);
    return createdRoles;
  } catch (error) {
    console.error('❌ Error initializing roles:', error);
    throw error;
  }
};

// Helper function to check if user has permission
export const hasPermission = (user, module, action, resource = '*') => {
  try {
    // Super admin bypass
    if (user.role?.level >= 10) {
      return true;
    }

    // Get user's permissions
    let userPermissions = [];
    
    if (user.permissions && user.permissions.length > 0) {
      userPermissions = user.permissions;
    } else if (user.role && user.role.defaultPermissions) {
      userPermissions = user.role.defaultPermissions;
    }

    // Check if user has the required permission
    return userPermissions.some(permission => {
      const perm = permission._id ? permission : permission;
      
      if (typeof perm === 'string') {
        return true; // Allow access for string permissions (fallback)
      }

      const moduleMatch = perm.module === module || perm.module === '*';
      const actionMatch = perm.action === action || perm.action === '*';
      const resourceMatch = perm.resource === resource || perm.resource === '*';

      return moduleMatch && actionMatch && resourceMatch;
    });
  } catch (error) {
    console.error('Permission check error:', error);
    return false;
  }
};

// Get permissions by category
export const getPermissionsByCategory = () => {
  const categories = {};
  
  SYSTEM_PERMISSIONS.forEach(permission => {
    if (!categories[permission.category]) {
      categories[permission.category] = [];
    }
    categories[permission.category].push(permission);
  });
  
  return categories;
};
