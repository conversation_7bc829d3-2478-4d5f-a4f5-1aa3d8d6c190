import Permission from '../models/Permission.js';
import Role from '../models/Role.js';
import connectDB from '../config/database.ts';

const permissions = [
  // Dashboard permissions
  { module: 'dashboard', action: 'view', resource: '*', description: 'View dashboard and statistics', category: 'Dashboard' },

  // Patient permissions
  { module: 'patients', action: 'view', resource: '*', description: 'View patient information', category: 'Patient Management' },
  { module: 'patients', action: 'create', resource: '*', description: 'Create new patients', category: 'Patient Management' },
  { module: 'patients', action: 'edit', resource: '*', description: 'Edit patient information', category: 'Patient Management' },
  { module: 'patients', action: 'delete', resource: '*', description: 'Delete patients', category: 'Patient Management' },
  { module: 'patients', action: 'export', resource: '*', description: 'Export patient data', category: 'Patient Management' },
  
  // Clinical permissions
  { module: 'clinical', action: 'view', resource: 'appointments', description: 'View appointments', category: 'Appointment Management' },
  { module: 'clinical', action: 'create', resource: 'appointments', description: 'Create appointments', category: 'Appointment Management' },
  { module: 'clinical', action: 'edit', resource: 'appointments', description: 'Edit appointments', category: 'Appointment Management' },
  { module: 'clinical', action: 'delete', resource: 'appointments', description: 'Delete appointments', category: 'Appointment Management' },
  { module: 'clinical', action: 'view', resource: 'schedules', description: 'View doctor schedules', category: 'Clinical Management' },
  { module: 'clinical', action: 'view', resource: 'medical-records', description: 'View medical records', category: 'Clinical Management' },
  { module: 'clinical', action: 'create', resource: 'medical-records', description: 'Create medical records', category: 'Clinical Management' },
  { module: 'clinical', action: 'edit', resource: 'medical-records', description: 'Edit medical records', category: 'Clinical Management' },

  // Laboratory permissions
  { module: 'laboratory', action: 'view', resource: '*', description: 'View lab tests and results', category: 'Laboratory Management' },
  { module: 'laboratory', action: 'create', resource: '*', description: 'Order lab tests', category: 'Laboratory Management' },
  { module: 'laboratory', action: 'edit', resource: '*', description: 'Update lab test results', category: 'Laboratory Management' },
  { module: 'laboratory', action: 'approve', resource: '*', description: 'Approve lab results', category: 'Laboratory Management' },

  // Pharmacy permissions
  { module: 'pharmacy', action: 'view', resource: '*', description: 'View pharmacy inventory and prescriptions', category: 'Pharmacy Management' },
  { module: 'pharmacy', action: 'create', resource: '*', description: 'Create prescriptions and manage inventory', category: 'Pharmacy Management' },
  { module: 'pharmacy', action: 'edit', resource: '*', description: 'Edit prescriptions and inventory', category: 'Pharmacy Management' },
  { module: 'pharmacy', action: 'delete', resource: '*', description: 'Delete prescriptions and inventory items', category: 'Pharmacy Management' },
  
  // Financial permissions
  { module: 'financial', action: 'view', resource: '*', description: 'View bills and financial data', category: 'Financial Management' },
  { module: 'financial', action: 'create', resource: '*', description: 'Create bills and process payments', category: 'Financial Management' },
  { module: 'financial', action: 'edit', resource: '*', description: 'Edit bills and financial records', category: 'Financial Management' },
  { module: 'financial', action: 'delete', resource: '*', description: 'Delete financial records', category: 'Financial Management' },
  { module: 'financial', action: 'export', resource: '*', description: 'Export financial reports', category: 'Financial Management' },

  // HR permissions
  { module: 'hr', action: 'view', resource: '*', description: 'View staff information and schedules', category: 'Human Resources' },
  { module: 'hr', action: 'create', resource: '*', description: 'Create staff records and schedules', category: 'Human Resources' },
  { module: 'hr', action: 'edit', resource: '*', description: 'Edit staff information', category: 'Human Resources' },
  { module: 'hr', action: 'delete', resource: '*', description: 'Delete staff records', category: 'Human Resources' },

  // Facility permissions
  { module: 'facility', action: 'view', resource: '*', description: 'View facility information and equipment', category: 'Facility Management' },
  { module: 'facility', action: 'create', resource: '*', description: 'Add facility resources and equipment', category: 'Facility Management' },
  { module: 'facility', action: 'edit', resource: '*', description: 'Edit facility information', category: 'Facility Management' },
  { module: 'facility', action: 'delete', resource: '*', description: 'Remove facility resources', category: 'Facility Management' },
  
  // Admin permissions
  { module: 'admin', action: 'view', resource: 'users', description: 'View user accounts', category: 'User Management' },
  { module: 'admin', action: 'create', resource: 'users', description: 'Create user accounts', category: 'User Management' },
  { module: 'admin', action: 'edit', resource: 'users', description: 'Edit user accounts', category: 'User Management' },
  { module: 'admin', action: 'delete', resource: 'users', description: 'Delete user accounts', category: 'User Management' },
  { module: 'admin', action: 'view', resource: 'roles', description: 'View roles and permissions', category: 'Role Management' },
  { module: 'admin', action: 'create', resource: 'roles', description: 'Create roles', category: 'Role Management' },
  { module: 'admin', action: 'edit', resource: 'roles', description: 'Edit roles and permissions', category: 'Role Management' },
  { module: 'admin', action: 'delete', resource: 'roles', description: 'Delete roles', category: 'Role Management' },
  { module: 'admin', action: 'view', resource: 'system', description: 'View system settings', category: 'System Administration' },
  { module: 'admin', action: 'edit', resource: 'system', description: 'Edit system settings', category: 'System Administration' },

  // Reports permissions
  { module: 'reports', action: 'view', resource: '*', description: 'View all reports', category: 'Reports' },
  { module: 'reports', action: 'create', resource: 'custom', description: 'Create custom reports', category: 'Reports' },
  { module: 'reports', action: 'export', resource: '*', description: 'Export reports', category: 'Reports' },
  { module: 'reports', action: 'view', resource: 'patient-analytics', description: 'View patient analytics', category: 'Reports' },
  { module: 'reports', action: 'view', resource: 'appointment-analytics', description: 'View appointment analytics', category: 'Reports' },
  { module: 'reports', action: 'view', resource: 'financial-analytics', description: 'View financial analytics', category: 'Reports' },
  { module: 'reports', action: 'view', resource: 'lab-analytics', description: 'View laboratory analytics', category: 'Reports' },
  { module: 'reports', action: 'view', resource: 'operational', description: 'View operational reports', category: 'Reports' },
  { module: 'reports', action: 'view', resource: 'clinical', description: 'View clinical reports', category: 'Reports' },
  { module: 'reports', action: 'view', resource: 'quality', description: 'View quality reports', category: 'Reports' }
];

const rolePermissions = {
  'Administrator': [
    // All permissions
    ...permissions.map(p => `${p.module}:${p.action}:${p.resource}`)
  ],
  'Doctor': [
    'dashboard:view:*',
    'patients:view:*', 'patients:create:*', 'patients:edit:*',
    'clinical:view:appointments', 'clinical:create:appointments', 'clinical:edit:appointments',
    'clinical:view:schedules', 'clinical:view:medical-records', 'clinical:create:medical-records', 'clinical:edit:medical-records',
    'laboratory:view:*', 'laboratory:create:*', 'laboratory:edit:*',
    'pharmacy:view:*', 'pharmacy:create:*',
    'reports:view:*', 'reports:view:patient-analytics', 'reports:view:clinical'
  ],
  'Nurse': [
    'dashboard:view:*',
    'patients:view:*', 'patients:edit:*',
    'clinical:view:appointments', 'clinical:edit:appointments',
    'clinical:view:medical-records', 'clinical:edit:medical-records',
    'laboratory:view:*', 'laboratory:edit:*',
    'pharmacy:view:*'
  ],
  'Receptionist': [
    'dashboard:view:*',
    'patients:view:*', 'patients:create:*', 'patients:edit:*',
    'clinical:view:appointments', 'clinical:create:appointments', 'clinical:edit:appointments',
    'clinical:view:schedules',
    'financial:view:*', 'financial:create:*', 'financial:edit:*'
  ]
};

export const seedPermissions = async () => {
  try {
    console.log('🔐 Starting permissions seeding...');
    
    await connectDB();
    
    // Clear existing permissions
    await Permission.deleteMany({});
    console.log('🗑️ Cleared existing permissions');
    
    // Create permissions
    const createdPermissions = await Permission.insertMany(permissions);
    console.log(`✅ Created ${createdPermissions.length} permissions`);
    
    // Update roles with permissions
    for (const [roleName, permissionKeys] of Object.entries(rolePermissions)) {
      const role = await Role.findOne({ name: roleName });
      if (role) {
        const rolePermissionIds = [];
        
        for (const permKey of permissionKeys) {
          const [module, action, resource] = permKey.split(':');
          const permission = await Permission.findOne({ module, action, resource });
          if (permission) {
            rolePermissionIds.push(permission._id);
          }
        }
        
        role.defaultPermissions = rolePermissionIds;
        await role.save();
        console.log(`✅ Updated ${roleName} role with ${rolePermissionIds.length} permissions`);
      }
    }
    
    console.log('🎉 Permissions seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding permissions:', error);
    throw error;
  }
};

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedPermissions()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}
