import express from 'express';
import {
  getAllUsers,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
  toggleUserStatus,
  getUserStats,
  getAllRoles,
  createRole,
  updateRole,
  deleteRole,
  getAuditLogs,
  createAuditLog,
  getSystemStats,
  getDepartments,
  getAvailablePermissions,
  getUserPermissions,
  checkUserPermission,
  // Enhanced permission system
  initializeSystem,
  getPermissionCategories,
  getAllPermissions,
  assignPermissionsToRole,
  assignRoleToUser,
  bulkUserOperations,
  getUserActivity,
  getAuditStats,
  exportAuditLogs
} from '../controllers/adminController.js';
import { protect, restrictTo } from '../controllers/authController.js';
import { checkPermission } from '../middleware/authMiddleware.js';
import { auditRoleCreate, auditRoleUpdate, auditRoleDelete, auditUserCreate, auditUserUpdate, auditUserDelete } from '../middleware/auditMiddleware.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// User management routes
router.route('/users')
  .get(checkPermission('admin', 'view', 'users'), getAllUsers)
  .post(checkPermission('admin', 'create', 'users'), auditUserCreate, createUser);

router.route('/users/:id')
  .put(checkPermission('admin', 'edit', 'users'), auditUserUpdate, updateUser)
  .delete(checkPermission('admin', 'delete', 'users'), auditUserDelete, deleteUser);

router.put('/users/:id/reset-password', checkPermission('admin', 'edit', 'users'), resetUserPassword);
router.put('/users/:id/toggle-status', checkPermission('admin', 'edit', 'users'), toggleUserStatus);
router.get('/users/stats', checkPermission('admin', 'view', 'users'), getUserStats);

// Role management routes
router.route('/roles')
  .get(checkPermission('admin', 'view', 'roles'), getAllRoles)
  .post(checkPermission('admin', 'create', 'roles'), auditRoleCreate, createRole);

router.route('/roles/:id')
  .put(checkPermission('admin', 'edit', 'roles'), auditRoleUpdate, updateRole)
  .delete(checkPermission('admin', 'delete', 'roles'), auditRoleDelete, deleteRole);

// Audit log routes
router.route('/audit-logs')
  .get(checkPermission('admin', 'view', 'system'), getAuditLogs)
  .post(checkPermission('admin', 'edit', 'system'), createAuditLog);

// System statistics
router.get('/system-stats', checkPermission('admin', 'view', 'system'), getSystemStats);

// Get departments list
router.get('/departments', getDepartments);

// ===== PERMISSION MANAGEMENT =====
// Get available permissions
router.get('/permissions', checkPermission('admin', 'view', 'roles'), getAvailablePermissions);

// Get user permissions
router.get('/users/:id/permissions', checkPermission('admin', 'view', 'users'), getUserPermissions);

// Check specific permission for user
router.get('/users/:id/permissions/:permission', checkPermission('admin', 'view', 'users'), checkUserPermission);

// ===== ENHANCED PERMISSION SYSTEM ROUTES =====
// System initialization (Super Admin only)
router.post('/initialize-system', checkPermission('admin', 'edit', 'system'), initializeSystem);

// Permission management
router.get('/permissions/categories', checkPermission('admin', 'view', 'roles'), getPermissionCategories);
router.get('/permissions/all', checkPermission('admin', 'view', 'roles'), getAllPermissions);

// Role and permission assignment
router.put('/roles/:id/permissions', checkPermission('admin', 'edit', 'roles'), assignPermissionsToRole);
router.put('/users/:id/role', checkPermission('admin', 'assign', 'users'), assignRoleToUser);

// Bulk operations
router.post('/users/bulk', checkPermission('admin', 'edit', 'users'), bulkUserOperations);

// User activity and analytics
router.get('/users/:id/activity', checkPermission('admin', 'view', 'users'), getUserActivity);

// Advanced audit system
router.get('/audit-stats', checkPermission('admin', 'view', 'audit'), getAuditStats);
router.get('/audit-logs/export', checkPermission('admin', 'export', 'audit'), exportAuditLogs);

export default router;
