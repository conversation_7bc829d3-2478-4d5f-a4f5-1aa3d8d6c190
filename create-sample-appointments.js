import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI;
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ Database connection error:', error);
    process.exit(1);
  }
};

// Define schemas
const UserSchema = new mongoose.Schema({
  username: String,
  email: String,
  firstName: String,
  lastName: String,
  role: { type: mongoose.Schema.Types.ObjectId, ref: 'Role' },
  department: String
}, { timestamps: true });

const PatientSchema = new mongoose.Schema({
  firstName: String,
  lastName: String,
  patientId: String
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);
const Patient = mongoose.model('Patient', PatientSchema);

const AppointmentSchema = new mongoose.Schema({
  appointmentId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'APT' + Date.now().toString().slice(-6);
    }
  },
  patient: { type: mongoose.Schema.Types.ObjectId, ref: 'Patient', required: true },
  doctor: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  department: {
    type: String,
    required: true,
    enum: ['Cardiology', 'Neurology', 'Orthopedics', 'Pediatrics', 'Gynecology', 'Dermatology', 'Psychiatry', 'Oncology', 'Emergency', 'General Medicine', 'Surgery', 'Radiology', 'Pathology']
  },
  appointmentDate: { type: Date, required: true },
  appointmentTime: { type: String, required: true },
  duration: { type: Number, default: 30, min: 15, max: 240 },
  type: { type: String, required: true, enum: ['Consultation', 'Follow-up', 'Emergency', 'Surgery', 'Diagnostic'] },
  status: { type: String, required: true, enum: ['Scheduled', 'Confirmed', 'In Progress', 'Completed', 'Cancelled', 'No Show'], default: 'Scheduled' },
  priority: { type: String, required: true, enum: ['Low', 'Medium', 'High', 'Emergency'], default: 'Medium' },
  reason: { type: String, required: true, trim: true },
  symptoms: [String],
  notes: { type: String, trim: true },
  diagnosis: { type: String, trim: true },
  prescription: [{
    medication: { type: String, required: true },
    dosage: { type: String, required: true },
    frequency: { type: String, required: true },
    duration: { type: String, required: true },
    instructions: String
  }],
  followUpDate: Date,
  room: String,
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
}, { timestamps: true });

const Appointment = mongoose.model('Appointment', AppointmentSchema);

const createSampleAppointments = async () => {
  await connectDB();
  
  try {
    // Clear existing appointments
    await Appointment.deleteMany({});
    
    // Get sample patients and doctors
    const patients = await Patient.find({}).limit(5);
    const doctors = await User.find({ 'role': { $exists: true } }).limit(2);
    
    if (patients.length === 0 || doctors.length === 0) {
      console.log('❌ No patients or doctors found. Please run seed script first.');
      process.exit(1);
    }
    
    const doctor = doctors[0]; // Use first doctor as creator and assigned doctor
    
    // Create appointments for the next few days
    const today = new Date();
    const sampleAppointments = [];
    
    // Create appointments for today and next 3 days
    for (let dayOffset = 0; dayOffset < 4; dayOffset++) {
      const appointmentDate = new Date(today);
      appointmentDate.setDate(today.getDate() + dayOffset);
      
      // Create 2-3 appointments per day
      const appointmentsPerDay = dayOffset === 0 ? 2 : 3;
      const times = ['09:00', '10:30', '14:00', '15:30', '16:30'];
      
      for (let i = 0; i < appointmentsPerDay && i < patients.length; i++) {
        const patient = patients[i];
        const time = times[i];
        
        const appointment = {
          patient: patient._id,
          doctor: doctor._id,
          department: 'General Medicine',
          appointmentDate: appointmentDate,
          appointmentTime: time,
          duration: 30,
          type: i === 0 ? 'Consultation' : 'Follow-up',
          status: dayOffset === 0 ? 'Confirmed' : 'Scheduled',
          priority: i === 0 ? 'High' : 'Medium',
          reason: `${i === 0 ? 'Initial consultation' : 'Follow-up visit'} for ${patient.firstName} ${patient.lastName}`,
          symptoms: i === 0 ? ['Headache', 'Fatigue'] : ['Improvement noted'],
          notes: `Appointment scheduled for ${patient.firstName} ${patient.lastName}`,
          room: `Room ${100 + i}`,
          createdBy: doctor._id
        };
        
        sampleAppointments.push(appointment);
      }
    }
    
    const createdAppointments = await Appointment.insertMany(sampleAppointments);
    console.log(`✅ Created ${createdAppointments.length} sample appointments`);
    
    // Display created appointments
    for (const appointment of createdAppointments) {
      const populatedAppointment = await Appointment.findById(appointment._id)
        .populate('patient', 'firstName lastName patientId')
        .populate('doctor', 'firstName lastName');
      
      console.log(`- ${populatedAppointment.appointmentId}: ${populatedAppointment.patient.firstName} ${populatedAppointment.patient.lastName} with Dr. ${populatedAppointment.doctor.firstName} ${populatedAppointment.doctor.lastName} on ${populatedAppointment.appointmentDate.toDateString()} at ${populatedAppointment.appointmentTime}`);
    }
    
  } catch (error) {
    console.error('Error creating sample appointments:', error);
  }
  
  process.exit(0);
};

createSampleAppointments();
